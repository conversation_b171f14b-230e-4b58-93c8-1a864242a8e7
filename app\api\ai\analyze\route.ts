import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import deepseekService from '@/lib/ai/deepseek'
import apiyiService from '@/lib/ai/apiyi'
import { ObjectId } from 'mongodb'

const deepseek = deepseekService
const apiyi = apiyiService

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const body = await request.json()
    
    const { type, content, fileId, projectId, options = {} } = body
    
    if (!type || !content) {
      return NextResponse.json(
        { success: false, error: '缺少必填参数' },
        { status: 400 }
      )
    }
    
    // 创建分析记录
    const analysisDoc = {
      type,
      content,
      fileId: fileId || null,
      projectId: projectId || null,
      status: 'running',
      progress: 0,
      options,
      createdAt: new Date(),
      createdBy: 'current-user', // TODO: 从认证中获取用户信息
      startedAt: new Date()
    }
    
    const result = await db.collection('ai_analyses').insertOne(analysisDoc)
    const analysisId = result.insertedId
    
    // 异步执行分析
    performAnalysis(analysisId.toString(), type, content, options)
    
    return NextResponse.json({
      success: true,
      data: {
        id: analysisId,
        ...analysisDoc
      }
    }, { status: 201 })
  } catch (error) {
    console.error('启动AI分析失败:', error)
    return NextResponse.json(
      { success: false, error: '启动AI分析失败' },
      { status: 500 }
    )
  }
}

async function performAnalysis(analysisId: string, type: string, content: string, options: any) {
  const { db } = await connectToDatabase()
  
  try {
    // 更新进度
    await updateProgress(analysisId, 20)
    
    let results: any = {}
    
    switch (type) {
      case 'document_analysis':
        results = await deepseek.analyzeTenderDocument(content)
        break
        
      case 'content_generation':
        const { projectType = '建设工程', requirements = [] } = options
        const companyInfo = { name: '示例公司', type: '建筑企业' }
        const projectInfo = { name: '示例项目', type: projectType }
        results = {
          content: await deepseek.generateBidSection(projectType, requirements, companyInfo, projectInfo),
          suggestions: [
            '建议根据具体项目需求调整内容',
            '可以添加更多技术细节',
            '注意检查格式和用词规范'
          ]
        }
        break
        
      case 'optimization':
        const optimizationRequirements = options?.requirements || ['提高专业性', '增强可读性', '完善逻辑结构']
        results = await deepseek.optimizeContent(content, optimizationRequirements)
        break
        
      case 'compliance_check':
        // 使用APIYI进行合规性检查
        await updateProgress(analysisId, 40)
        const complianceResults = await apiyi.reviewContent(content, options.requirements || ['法规合规性', '技术标准', '格式规范'])
        results = {
          score: complianceResults.score,
          issues: complianceResults.issues,
          improvements: complianceResults.suggestions,
          summary: `合规性检查完成，综合评分 ${complianceResults.score} 分`
        }
        break
        
      default:
        throw new Error(`不支持的分析类型: ${type}`)
    }
    
    await updateProgress(analysisId, 80)
    
    // 保存分析结果
    await db.collection('ai_analyses').updateOne(
      { _id: new ObjectId(analysisId) },
      {
        $set: {
          status: 'completed',
          progress: 100,
          results,
          completedAt: new Date(),
          duration: Date.now() - new Date().getTime()
        }
      }
    )
    
  } catch (error) {
    console.error('AI分析执行失败:', error)
    
    // 标记为失败
    await db.collection('ai_analyses').updateOne(
      { _id: new ObjectId(analysisId) },
      {
        $set: {
          status: 'failed',
          error: error instanceof Error ? error.message : '分析失败',
          completedAt: new Date()
        }
      }
    )
  }
}

async function updateProgress(analysisId: string, progress: number) {
  const { db } = await connectToDatabase()
  await db.collection('ai_analyses').updateOne(
    { _id: new ObjectId(analysisId) },
    { $set: { progress, updatedAt: new Date() } }
  )
}

export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const projectId = searchParams.get('projectId')
    
    // 构建查询条件
    const query: any = {}
    if (type && type !== 'all') {
      query.type = type
    }
    if (status && status !== 'all') {
      query.status = status
    }
    if (projectId) {
      query.projectId = projectId
    }
    
    // 分页查询
    const skip = (page - 1) * limit
    const analyses = await db.collection('ai_analyses')
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray()
    
    const total = await db.collection('ai_analyses').countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: {
        analyses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取分析历史失败:', error)
    return NextResponse.json(
      { success: false, error: '获取分析历史失败' },
      { status: 500 }
    )
  }
}
