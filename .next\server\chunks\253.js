exports.id=253,exports.ids=[253],exports.modules={9345:(e,t,s)=>{Promise.resolve().then(s.bind(s,9923)),Promise.resolve().then(s.bind(s,5813)),Promise.resolve().then(s.bind(s,1245))},9433:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},9923:(e,t,s)=>{"use strict";s.d(t,{default:()=>T});var r=s(326),a=s(7577),i=s(434),n=s(5047),d=s(772),l=s(4319),o=s(8932),c=s(6283),x=s(3),m=s(8360),h=s(9163),u=s(8307),f=s(9635),g=s(748),v=s(850),p=s(2607),b=s(6507),y=s(8378),j=s(4019),N=s(1810),w=s(4831);let k=[{name:"仪表板",href:"/dashboard",icon:l.Z},{name:"项目管理",href:"/projects",icon:o.Z},{name:"文件管理",href:"/files",icon:c.Z},{name:"文档编辑",href:"/editor",icon:x.Z},{name:"AI分析",href:"/ai",icon:m.Z},{name:"模板管理",href:"/templates",icon:h.Z},{name:"智能搜索",href:"/search",icon:u.Z}];function T(){let[e,t]=(0,a.useState)(!1),s=(0,n.usePathname)(),{theme:l,setTheme:o}=(0,w.F)();return(0,r.jsxs)(r.Fragment,{children:[r.jsx("nav",{className:"hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:border-r lg:border-gray-200 lg:bg-white lg:dark:bg-gray-800 lg:dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto",children:[r.jsx("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(c.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"投标编辑器"}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"AI智能投标系统"})]})]})}),r.jsx("nav",{className:"mt-8 flex-1 px-2 space-y-1",children:k.map(e=>{let t=s===e.href,a=e.icon;return(0,r.jsxs)(i.default,{href:e.href,className:`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${t?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"}`,children:[r.jsx(a,{className:`mr-3 h-5 w-5 flex-shrink-0 ${t?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"}`}),e.name]},e.name)})}),r.jsx("div",{className:"flex-shrink-0 flex border-t border-gray-200 dark:border-gray-700 p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:r.jsx(f.Z,{className:"w-4 h-4 text-gray-600"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:"当前用户"}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})})]})}),r.jsx("div",{className:"lg:hidden",children:(0,r.jsxs)("div",{className:"flex items-center justify-between bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("button",{type:"button",className:"text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300",onClick:()=>t(!0),children:r.jsx(g.Z,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{className:"ml-3 flex items-center",children:[r.jsx("div",{className:"w-6 h-6 bg-blue-600 rounded flex items-center justify-center",children:r.jsx(c.Z,{className:"w-4 h-4 text-white"})}),r.jsx("h1",{className:"ml-2 text-lg font-semibold text-gray-900 dark:text-white",children:"投标编辑器"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.z,{variant:"ghost",size:"icon",onClick:()=>o("dark"===l?"light":"dark"),children:"dark"===l?r.jsx(v.Z,{className:"h-4 w-4"}):r.jsx(p.Z,{className:"h-4 w-4"})}),r.jsx(d.z,{variant:"ghost",size:"icon",children:r.jsx(b.Z,{className:"h-4 w-4"})}),r.jsx(d.z,{variant:"ghost",size:"icon",children:r.jsx(y.Z,{className:"h-4 w-4"})})]})]})}),e&&r.jsx("div",{className:"lg:hidden",children:(0,r.jsxs)("div",{className:"fixed inset-0 flex z-40",children:[r.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>t(!1)}),(0,r.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-gray-800",children:[r.jsx("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:r.jsx("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>t(!1),children:r.jsx(j.Z,{className:"h-6 w-6 text-white"})})}),(0,r.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[r.jsx("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(c.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"投标编辑器"}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"AI智能投标系统"})]})]})}),r.jsx("nav",{className:"mt-8 px-2 space-y-1",children:k.map(e=>{let a=s===e.href,n=e.icon;return(0,r.jsxs)(i.default,{href:e.href,className:`group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ${a?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"}`,onClick:()=>t(!1),children:[r.jsx(n,{className:`mr-4 h-6 w-6 flex-shrink-0 ${a?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"}`}),e.name]},e.name)})})]}),r.jsx("div",{className:"flex-shrink-0 flex border-t border-gray-200 dark:border-gray-700 p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:r.jsx(f.Z,{className:"w-4 h-4 text-gray-600"})}),(0,r.jsxs)("div",{className:"ml-3",children:[r.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:"当前用户"}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})})]})]})}),r.jsx("div",{className:"hidden lg:block lg:pl-64",children:r.jsx("div",{className:"flex items-center justify-end bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.z,{variant:"ghost",size:"icon",onClick:()=>o("dark"===l?"light":"dark"),children:"dark"===l?r.jsx(v.Z,{className:"h-4 w-4"}):r.jsx(p.Z,{className:"h-4 w-4"})}),r.jsx(d.z,{variant:"ghost",size:"icon",children:r.jsx(b.Z,{className:"h-4 w-4"})}),r.jsx(d.z,{variant:"ghost",size:"icon",children:r.jsx(y.Z,{className:"h-4 w-4"})}),r.jsx(d.z,{variant:"ghost",size:"icon",children:r.jsx(N.Z,{className:"h-4 w-4"})})]})})})]})}},5813:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>i});var r=s(326);s(7577);var a=s(4831);function i({children:e,...t}){return r.jsx(a.f,{...t,children:e})}},772:(e,t,s)=>{"use strict";s.d(t,{z:()=>o});var r=s(326),a=s(7577),i=s(4214),n=s(9360),d=s(9310);let l=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...n},o)=>{let c=a?i.g7:"button";return r.jsx(c,{className:(0,d.cn)(l({variant:t,size:s,className:e})),ref:o,...n})});o.displayName="Button"},1245:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>k});var r=s(326),a=s(7577);let i=0,n=new Map,d=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),x({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?d(s):e.toasts.forEach(e=>{d(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function x(e){c=l(c,e),o.forEach(e=>{e(c)})}function m({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>x({type:"DISMISS_TOAST",toastId:t});return x({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>x({type:"UPDATE_TOAST",toast:{...e,id:t}})}}var h=s(4578),u=s(9360),f=s(4019),g=s(9310);let v=h.zt,p=a.forwardRef(({className:e,...t},s)=>r.jsx(h.l_,{ref:s,className:(0,g.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));p.displayName=h.l_.displayName;let b=(0,u.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=a.forwardRef(({className:e,variant:t,...s},a)=>r.jsx(h.fC,{ref:a,className:(0,g.cn)(b({variant:t}),e),...s}));y.displayName=h.fC.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(h.aU,{ref:s,className:(0,g.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=h.aU.displayName;let j=a.forwardRef(({className:e,...t},s)=>r.jsx(h.x8,{ref:s,className:(0,g.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:r.jsx(f.Z,{className:"h-4 w-4"})}));j.displayName=h.x8.displayName;let N=a.forwardRef(({className:e,...t},s)=>r.jsx(h.Dx,{ref:s,className:(0,g.cn)("text-sm font-semibold",e),...t}));N.displayName=h.Dx.displayName;let w=a.forwardRef(({className:e,...t},s)=>r.jsx(h.dk,{ref:s,className:(0,g.cn)("text-sm opacity-90",e),...t}));function k(){let{toasts:e}=function(){let[e,t]=a.useState(c);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>x({type:"DISMISS_TOAST",toastId:e})}}();return(0,r.jsxs)(v,{children:[e.map(function({id:e,title:t,description:s,action:a,...i}){return(0,r.jsxs)(y,{...i,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&r.jsx(N,{children:t}),s&&r.jsx(w,{children:s})]}),a,r.jsx(j,{})]},e)}),r.jsx(p,{})]})}w.displayName=h.dk.displayName},9310:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(1135),a=s(1009);function i(...e){return(0,a.m6)((0,r.W)(e))}},6070:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>c});var r=s(9510),a=s(7366),i=s.n(a);s(7272);var n=s(8570);let d=(0,n.createProxy)(String.raw`E:\rjkf\tb-0704-V\components\theme-provider.tsx#ThemeProvider`),l=(0,n.createProxy)(String.raw`E:\rjkf\tb-0704-V\components\ui\toaster.tsx#Toaster`),o=(0,n.createProxy)(String.raw`E:\rjkf\tb-0704-V\components\navigation.tsx#default`),c={title:"投标文件编辑器",description:"所见即所得的智能投标文件生成系统",keywords:["投标","文件编辑器","AI","招标","投标文件生成"],authors:[{name:"投标文件编辑器团队"}],viewport:"width=device-width, initial-scale=1"};function x({children:e}){return r.jsx("html",{lang:"zh-CN",suppressHydrationWarning:!0,children:r.jsx("body",{className:i().className,children:(0,r.jsxs)(d,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(o,{}),r.jsx("main",{className:"lg:pl-64",children:e})]}),r.jsx(l,{})]})})})}},7272:()=>{}};