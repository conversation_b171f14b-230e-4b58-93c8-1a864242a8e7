# MongoDB 数据库配置
MONGODB_URI=mongodb://localhost:27017/tender-editor
MONGODB_DB=tender-editor

# Neo4j 图数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# DeepSeek AI API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_URL=https://api.deepseek.com

# APIYI AI API 配置
APIYI_API_KEY=your_apiyi_api_key_here
APIYI_API_URL=https://api.apiyi.com

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif

# 应用配置
NEXT_PUBLIC_APP_NAME=投标文件编辑器
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# 安全配置
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Redis 缓存配置（可选）
REDIS_URL=redis://localhost:6379

# 开发环境配置
NODE_ENV=development
PORT=3000
