'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Brain, 
  Search, 
  Upload, 
  BarChart3, 
  Settings,
  Plus,
  ArrowRight,
  Sparkles,
  Target,
  Clock,
  Users,
  TrendingUp,
  Activity,
  Calendar,
  CheckCircle
} from 'lucide-react'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalDocuments: 0,
    aiAnalyses: 0,
    templatesUsed: 0
  })

  const [recentActivities, setRecentActivities] = useState<any[]>([])
  const [upcomingDeadlines, setUpcomingDeadlines] = useState<any[]>([])

  useEffect(() => {
    // 模拟加载数据
    setStats({
      totalProjects: 12,
      activeProjects: 5,
      completedProjects: 7,
      totalDocuments: 48,
      aiAnalyses: 23,
      templatesUsed: 15
    })

    setRecentActivities([
      {
        id: 1,
        type: 'project_created',
        title: '创建了新项目：某市政道路建设项目',
        time: '2小时前',
        icon: Plus
      },
      {
        id: 2,
        type: 'ai_analysis',
        title: 'AI分析完成：办公楼装修工程招标文件',
        time: '4小时前',
        icon: Brain
      },
      {
        id: 3,
        type: 'document_uploaded',
        title: '上传了新文档：技术规范说明书',
        time: '6小时前',
        icon: Upload
      },
      {
        id: 4,
        type: 'project_completed',
        title: '完成项目：IT设备采购项目',
        time: '1天前',
        icon: CheckCircle
      }
    ])

    setUpcomingDeadlines([
      {
        id: 1,
        project: '某市政道路建设项目',
        deadline: '2024-07-15',
        daysLeft: 11,
        status: 'urgent'
      },
      {
        id: 2,
        project: '办公楼装修工程',
        deadline: '2024-07-20',
        daysLeft: 16,
        status: 'warning'
      },
      {
        id: 3,
        project: '学校设备采购',
        deadline: '2024-07-25',
        daysLeft: 21,
        status: 'normal'
      }
    ])
  }, [])

  const getActivityIcon = (type: string) => {
    const iconMap = {
      project_created: Plus,
      ai_analysis: Brain,
      document_uploaded: Upload,
      project_completed: CheckCircle
    }
    return iconMap[type as keyof typeof iconMap] || Activity
  }

  const getDeadlineStatus = (status: string) => {
    const statusMap = {
      urgent: { color: 'bg-red-100 text-red-800', label: '紧急' },
      warning: { color: 'bg-yellow-100 text-yellow-800', label: '注意' },
      normal: { color: 'bg-green-100 text-green-800', label: '正常' }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.normal
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 导航栏 */}
      <nav className="border-b bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Sparkles className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                  投标文件编辑器
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/projects">
                <Button variant="ghost">项目管理</Button>
              </Link>
              <Link href="/files">
                <Button variant="ghost">文件管理</Button>
              </Link>
              <Link href="/ai">
                <Button variant="ghost">AI分析</Button>
              </Link>
              <Link href="/settings">
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">仪表板</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            欢迎回来！这里是您的项目概览和最新动态。
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总项目数</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProjects}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +2 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">进行中</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeProjects}</div>
              <p className="text-xs text-muted-foreground">
                +1 较上周
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已完成</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedProjects}</div>
              <p className="text-xs text-muted-foreground">
                +3 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">文档总数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDocuments}</div>
              <p className="text-xs text-muted-foreground">
                +12 较上周
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI分析</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.aiAnalyses}</div>
              <p className="text-xs text-muted-foreground">
                +8 较上周
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">模板使用</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.templatesUsed}</div>
              <p className="text-xs text-muted-foreground">
                +5 较上周
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 最近活动 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5" />
                  最近活动
                </CardTitle>
                <CardDescription>
                  查看您最近的项目活动和操作记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity: any) => {
                    const IconComponent = getActivityIcon(activity.type)
                    return (
                      <div key={activity.id} className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <IconComponent className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {activity.time}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
                <div className="mt-6">
                  <Link href="/activities">
                    <Button variant="outline" className="w-full">
                      查看全部活动
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 即将到期的项目 */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  即将到期
                </CardTitle>
                <CardDescription>
                  需要关注的项目截止日期
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingDeadlines.map((deadline: any) => {
                    const statusInfo = getDeadlineStatus(deadline.status)
                    return (
                      <div key={deadline.id} className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {deadline.project}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {deadline.deadline}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">
                            {deadline.daysLeft}天
                          </span>
                          <Badge className={statusInfo.color}>
                            {statusInfo.label}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
                <div className="mt-6">
                  <Link href="/projects">
                    <Button variant="outline" className="w-full">
                      查看所有项目
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
                <CardDescription>
                  常用功能快速入口
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/projects/new">
                  <Button className="w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    创建新项目
                  </Button>
                </Link>
                <Link href="/files/upload">
                  <Button variant="outline" className="w-full justify-start">
                    <Upload className="mr-2 h-4 w-4" />
                    上传文件
                  </Button>
                </Link>
                <Link href="/ai/analysis">
                  <Button variant="outline" className="w-full justify-start">
                    <Brain className="mr-2 h-4 w-4" />
                    AI分析
                  </Button>
                </Link>
                <Link href="/search">
                  <Button variant="outline" className="w-full justify-start">
                    <Search className="mr-2 h-4 w-4" />
                    搜索内容
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
