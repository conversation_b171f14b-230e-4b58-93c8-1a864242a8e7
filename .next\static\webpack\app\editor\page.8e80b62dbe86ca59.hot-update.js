"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/page",{

/***/ "(app-pages-browser)/./components/editor/wysiwyg-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/wysiwyg-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WysiwygEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bold.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/italic.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/underline.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-center.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,Bold,Download,FileText,Image,Italic,Link,List,ListOrdered,Palette,Redo,Save,Underline,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction WysiwygEditor(param) {\n    let { initialContent = \"\", onSave, onContentChange, placeholder = \"开始编写您的投标文档...\", readOnly = false } = param;\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialContent);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (editorRef.current && initialContent) {\n            editorRef.current.innerHTML = initialContent;\n        }\n    }, [\n        initialContent\n    ]);\n    const executeCommand = (command, value)=>{\n        document.execCommand(command, false, value);\n        handleContentChange();\n    };\n    const handleContentChange = ()=>{\n        if (editorRef.current) {\n            const newContent = editorRef.current.innerHTML;\n            setContent(newContent);\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(newContent);\n        }\n    };\n    const handleSave = ()=>{\n        onSave === null || onSave === void 0 ? void 0 : onSave(content);\n        setIsEditing(false);\n    };\n    // 文件导入处理\n    const handleFileImport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        acceptedFiles.forEach((file)=>{\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const content = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                let htmlContent = \"\";\n                if (file.type === \"text/plain\") {\n                    // 处理纯文本文件\n                    htmlContent = content.replace(/\\n/g, \"<br>\");\n                } else if (file.type === \"text/html\") {\n                    // 处理HTML文件\n                    htmlContent = content;\n                } else if (file.name.endsWith(\".md\")) {\n                    // 简单的Markdown转HTML处理\n                    htmlContent = content.replace(/^# (.*$)/gim, \"<h1>$1</h1>\").replace(/^## (.*$)/gim, \"<h2>$1</h2>\").replace(/^### (.*$)/gim, \"<h3>$1</h3>\").replace(/\\*\\*(.*)\\*\\*/gim, \"<strong>$1</strong>\").replace(/\\*(.*)\\*/gim, \"<em>$1</em>\").replace(/\\n/g, \"<br>\");\n                } else {\n                    // 其他文件类型作为纯文本处理\n                    htmlContent = content.replace(/\\n/g, \"<br>\");\n                }\n                // 将内容插入到编辑器中\n                if (editorRef.current) {\n                    const currentContent = editorRef.current.innerHTML;\n                    const newContent = currentContent + (currentContent ? \"<br><br>\" : \"\") + htmlContent;\n                    editorRef.current.innerHTML = newContent;\n                    setContent(newContent);\n                    onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(newContent);\n                    setIsEditing(true);\n                }\n            };\n            reader.onerror = ()=>{\n                alert(\"文件读取失败，请重试\");\n            };\n            // 根据文件类型选择读取方式\n            if (file.type.startsWith(\"text/\") || file.name.endsWith(\".md\")) {\n                reader.readAsText(file, \"UTF-8\");\n            } else {\n                alert(\"不支持的文件格式。请选择文本文件(.txt)、HTML文件(.html)或Markdown文件(.md)\");\n            }\n        });\n    }, [\n        onContentChange\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_4__.useDropzone)({\n        onDrop: handleFileImport,\n        accept: {\n            \"text/plain\": [\n                \".txt\"\n            ],\n            \"text/html\": [\n                \".html\",\n                \".htm\"\n            ],\n            \"text/markdown\": [\n                \".md\"\n            ],\n            \"application/json\": [\n                \".json\"\n            ]\n        },\n        multiple: true,\n        noClick: true // 禁用点击，只允许拖拽\n    });\n    // 手动触发文件选择\n    const triggerFileSelect = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".txt,.html,.htm,.md,.json\";\n        input.multiple = true;\n        input.onchange = (e)=>{\n            const files = Array.from(e.target.files || []);\n            if (files.length > 0) {\n                handleFileImport(files);\n            }\n        };\n        input.click();\n    };\n    const insertImage = ()=>{\n        const url = prompt(\"请输入图片URL:\");\n        if (url) {\n            executeCommand(\"insertImage\", url);\n        }\n    };\n    const insertLink = ()=>{\n        const url = prompt(\"请输入链接URL:\");\n        if (url) {\n            executeCommand(\"createLink\", url);\n        }\n    };\n    const changeFontSize = (size)=>{\n        executeCommand(\"fontSize\", size);\n    };\n    const changeTextColor = (color)=>{\n        executeCommand(\"foreColor\", color);\n    };\n    const toolbarButtons = [\n        {\n            group: \"格式\",\n            buttons: [\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    command: \"bold\",\n                    title: \"粗体\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    command: \"italic\",\n                    title: \"斜体\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    command: \"underline\",\n                    title: \"下划线\"\n                }\n            ]\n        },\n        {\n            group: \"对齐\",\n            buttons: [\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    command: \"justifyLeft\",\n                    title: \"左对齐\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    command: \"justifyCenter\",\n                    title: \"居中对齐\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    command: \"justifyRight\",\n                    title: \"右对齐\"\n                }\n            ]\n        },\n        {\n            group: \"列表\",\n            buttons: [\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    command: \"insertUnorderedList\",\n                    title: \"无序列表\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    command: \"insertOrderedList\",\n                    title: \"有序列表\"\n                }\n            ]\n        },\n        {\n            group: \"插入\",\n            buttons: [\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    action: insertLink,\n                    title: \"插入链接\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    action: insertImage,\n                    title: \"插入图片\"\n                }\n            ]\n        },\n        {\n            group: \"操作\",\n            buttons: [\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    command: \"undo\",\n                    title: \"撤销\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    command: \"redo\",\n                    title: \"重做\"\n                }\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                \"文档编辑器\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: !readOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: triggerFileSelect,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"导入\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"导出\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"保存\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            !readOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 dark:border-gray-700 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-4\",\n                    children: [\n                        toolbarButtons.map((group, groupIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    group.buttons.map((button, buttonIndex)=>{\n                                        const Icon = button.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                if (\"command\" in button) {\n                                                    executeCommand(button.command);\n                                                } else if (\"action\" in button) {\n                                                    button.action();\n                                                }\n                                            },\n                                            title: button.title,\n                                            className: \"h-8 w-8 p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, buttonIndex, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    groupIndex < toolbarButtons.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, groupIndex, true, {\n                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            onChange: (e)=>changeFontSize(e.target.value),\n                            className: \"px-2 py-1 border border-gray-300 rounded text-sm bg-white dark:bg-gray-800 dark:border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"1\",\n                                    children: \"小\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"3\",\n                                    selected: true,\n                                    children: \"正常\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"5\",\n                                    children: \"大\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"7\",\n                                    children: \"特大\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"color\",\n                                    onChange: (e)=>changeTextColor(e.target.value),\n                                    className: \"w-8 h-8 border border-gray-300 rounded cursor-pointer\",\n                                    title: \"文字颜色\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-0\",\n                ...getRootProps(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: editorRef,\n                    contentEditable: !readOnly,\n                    onInput: handleContentChange,\n                    onFocus: ()=>setIsEditing(true),\n                    className: \"min-h-[400px] p-6 focus:outline-none relative \".concat(readOnly ? \"cursor-default\" : \"cursor-text\", \" \").concat(isDragActive ? \"bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-500\" : \"\"),\n                    style: {\n                        lineHeight: \"1.6\",\n                        fontSize: \"16px\"\n                    },\n                    suppressContentEditableWarning: true,\n                    children: [\n                        isDragActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center bg-blue-50/90 dark:bg-blue-900/40 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_Bold_Download_FileText_Image_Italic_Link_List_ListOrdered_Palette_Redo_Save_Underline_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-blue-500 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 dark:text-blue-400 font-medium\",\n                                        children: \"释放文件以导入内容\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-500 dark:text-blue-300\",\n                                        children: \"支持 .txt, .html, .md 文件\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this),\n                        !content && !readOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 pointer-events-none\",\n                            children: placeholder\n                        }, void 0, false, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 dark:border-gray-700 px-6 py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"字数: \",\n                                        content.replace(/<[^>]*>/g, \"\").length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"段落: \",\n                                        (content.match(/<p>/g) || []).length || 1\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 dark:text-blue-400\",\n                                    children: \"正在编辑...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"最后保存: 刚刚\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\components\\\\editor\\\\wysiwyg-editor.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(WysiwygEditor, \"NKYfe8pT1S12mozsUhk9L0S4n4w=\", false, function() {\n    return [\n        react_dropzone__WEBPACK_IMPORTED_MODULE_4__.useDropzone\n    ];\n});\n_c = WysiwygEditor;\nvar _c;\n$RefreshReg$(_c, \"WysiwygEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/editor/wysiwyg-editor.tsx\n"));

/***/ })

});