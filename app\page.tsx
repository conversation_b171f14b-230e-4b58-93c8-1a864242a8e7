'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import WysiwygEditor from '@/components/editor/wysiwyg-editor'
import {
  FileText,
  Brain,
  Search,
  Upload,
  BarChart3,
  Settings,
  Plus,
  FolderOpen,
  Database,
  Users,
  Building,
  Image,
  Layers,
  Target,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'

export default function HomePage() {
  const [currentDocument, setCurrentDocument] = useState({
    id: '',
    title: '新建投标文件',
    content: '',
    wordCount: 0,
    targetWords: 150000, // 15万字目标
    chapters: []
  })

  const [selectedFiles, setSelectedFiles] = useState([])
  const [analysisResults, setAnalysisResults] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [activePanel, setActivePanel] = useState('files') // files, analysis, templates, entities

  useEffect(() => {
    // 初始化文档
    setCurrentDocument(prev => ({
      ...prev,
      id: `doc_${Date.now()}`,
      content: '<h1>投标文件</h1><p>请先上传招标文件或从数据库选择文件开始编辑...</p>'
    }))
  }, [])

  // 文件上传处理
  const handleFileUpload = async (files) => {
    setIsAnalyzing(true)
    // 模拟AI分析过程
    setTimeout(() => {
      setAnalysisResults({
        chapters: [
          { id: 1, title: '项目概况', status: 'analyzed', content: '项目基本信息已解析...' },
          { id: 2, title: '技术方案', status: 'analyzing', content: '正在分析技术要求...' },
          { id: 3, title: '施工组织设计', status: 'pending', content: '等待分析...' },
          { id: 4, title: '质量保证措施', status: 'pending', content: '等待分析...' },
          { id: 5, title: '安全文明施工', status: 'pending', content: '等待分析...' },
          { id: 6, title: '工期保证措施', status: 'pending', content: '等待分析...' }
        ],
        totalWords: 0,
        targetWords: 150000
      })
      setIsAnalyzing(false)
    }, 3000)
  }

  // 侧边栏功能面板
  const sidebarPanels = [
    {
      id: 'files',
      title: '招标文件',
      icon: FolderOpen,
      description: '上传或选择招标文件'
    },
    {
      id: 'entities',
      title: '主体管理',
      icon: Users,
      description: '投标方、供应商、联合体'
    },
    {
      id: 'analysis',
      title: 'AI分析',
      icon: Brain,
      description: '逐章节智能解析'
    },
    {
      id: 'templates',
      title: '模板库',
      icon: Layers,
      description: '投标文件模板'
    },
    {
      id: 'diagrams',
      title: '施工图形',
      icon: Image,
      description: '施工示意图生成'
    }
  ]

  const quickActions = [
    {
      title: '创建新项目',
      description: '开始一个新的投标项目',
      icon: Plus,
      href: '/projects/new',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: '上传招标文件',
      description: '上传并分析招标文件',
      icon: Upload,
      href: '/files/upload',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: '浏览模板',
      description: '查看可用的投标模板',
      icon: FileText,
      href: '/templates',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: 'AI分析',
      description: '使用AI分析现有文档',
      icon: Brain,
      href: '/ai/analysis',
      color: 'bg-orange-600 hover:bg-orange-700'
    }
  ]

  const recentProjects = [
    {
      id: '1',
      name: '某市政道路建设项目',
      status: 'in_progress',
      progress: 75,
      deadline: '2024-07-15',
      type: 'construction'
    },
    {
      id: '2',
      name: '办公楼装修工程',
      status: 'review',
      progress: 90,
      deadline: '2024-07-20',
      type: 'construction'
    },
    {
      id: '3',
      name: 'IT设备采购项目',
      status: 'completed',
      progress: 100,
      deadline: '2024-06-30',
      type: 'goods'
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: '草稿', variant: 'secondary' as const },
      in_progress: { label: '进行中', variant: 'default' as const },
      review: { label: '审核中', variant: 'outline' as const },
      completed: { label: '已完成', variant: 'secondary' as const },
      archived: { label: '已归档', variant: 'secondary' as const }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.draft
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* 顶部工具栏 */}
      <header className="border-b bg-white dark:bg-gray-800 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-blue-600" />
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                投标文件编辑器
              </h1>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <span>当前文档:</span>
              <span className="font-medium">{currentDocument.title}</span>
              <Badge variant="outline">
                {currentDocument.wordCount.toLocaleString()} / {currentDocument.targetWords.toLocaleString()} 字
              </Badge>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="mr-1 h-4 w-4" />
              上传招标文件
            </Button>
            <Button variant="outline" size="sm">
              <Database className="mr-1 h-4 w-4" />
              数据库文件
            </Button>
            <Button size="sm">
              <CheckCircle className="mr-1 h-4 w-4" />
              保存文档
            </Button>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1 flex overflow-hidden">
        {/* 左侧功能面板 */}
        <aside className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* 功能选项卡 */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-1 p-2">
              {sidebarPanels.map((panel) => (
                <button
                  key={panel.id}
                  onClick={() => setActivePanel(panel.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activePanel === panel.id
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                  }`}
                >
                  <panel.icon className="h-4 w-4" />
                  <span className="hidden lg:block">{panel.title}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* 功能面板内容 */}
          <div className="flex-1 overflow-y-auto p-4">
            {activePanel === 'files' && (
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">招标文件管理</h3>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleFileUpload([])}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    上传新文件
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Database className="mr-2 h-4 w-4" />
                    从数据库选择
                  </Button>
                </div>
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最近文件</h4>
                  <div className="space-y-1">
                    <div className="p-2 rounded border border-gray-200 dark:border-gray-600 text-sm">
                      某市政道路建设招标文件.pdf
                    </div>
                    <div className="p-2 rounded border border-gray-200 dark:border-gray-600 text-sm">
                      办公楼装修工程招标文件.docx
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activePanel === 'entities' && (
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">主体管理</h3>
                <div className="space-y-3">
                  <div className="p-3 border border-gray-200 dark:border-gray-600 rounded">
                    <div className="flex items-center space-x-2 mb-2">
                      <Building className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">投标方</span>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      <Upload className="mr-1 h-3 w-3" />
                      上传资质文件
                    </Button>
                  </div>
                  <div className="p-3 border border-gray-200 dark:border-gray-600 rounded">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-4 w-4 text-green-600" />
                      <span className="font-medium">供应商</span>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      <Upload className="mr-1 h-3 w-3" />
                      上传供应商资料
                    </Button>
                  </div>
                  <div className="p-3 border border-gray-200 dark:border-gray-600 rounded">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-4 w-4 text-purple-600" />
                      <span className="font-medium">联合体</span>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      <Upload className="mr-1 h-3 w-3" />
                      上传联合体协议
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {activePanel === 'analysis' && (
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">AI分析进度</h3>
                {isAnalyzing ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">正在分析招标文件...</p>
                  </div>
                ) : analysisResults ? (
                  <div className="space-y-2">
                    {analysisResults.chapters.map((chapter) => (
                      <div key={chapter.id} className="p-2 border border-gray-200 dark:border-gray-600 rounded">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{chapter.title}</span>
                          {chapter.status === 'analyzed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                          {chapter.status === 'analyzing' && <Clock className="h-4 w-4 text-yellow-600" />}
                          {chapter.status === 'pending' && <AlertCircle className="h-4 w-4 text-gray-400" />}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-600 dark:text-gray-400">请先上传招标文件进行分析</p>
                )}
              </div>
            )}

            {activePanel === 'templates' && (
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">投标模板库</h3>
                <div className="space-y-2">
                  <div className="p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="font-medium text-sm">建筑工程投标模板</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">适用于建筑施工项目</div>
                  </div>
                  <div className="p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="font-medium text-sm">市政工程投标模板</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">适用于市政基础设施</div>
                  </div>
                  <div className="p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="font-medium text-sm">装修工程投标模板</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">适用于装修装饰项目</div>
                  </div>
                </div>
              </div>
            )}

            {activePanel === 'diagrams' && (
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">施工图形生成</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Image className="mr-2 h-4 w-4" />
                    施工示意图
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Layers className="mr-2 h-4 w-4" />
                    工程节点大样图
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Target className="mr-2 h-4 w-4" />
                    现场总平面布置图
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Settings className="mr-2 h-4 w-4" />
                    主要设备布置图
                  </Button>
                </div>
              </div>
            )}
          </div>
        </aside>

        {/* 中间编辑器区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <WysiwygEditor
            initialContent={currentDocument.content}
            onContentChange={(content) => {
              const wordCount = content.replace(/<[^>]*>/g, '').length
              setCurrentDocument(prev => ({
                ...prev,
                content,
                wordCount
              }))
            }}
            onSave={(content) => {
              console.log('保存文档:', content)
            }}
            placeholder="开始编写您的投标文件..."
          />
        </div>

        {/* 右侧状态面板 */}
        <aside className="w-64 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
          <div className="space-y-6">
            {/* 文档状态 */}
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">文档状态</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>当前字数</span>
                  <span className="font-medium">{currentDocument.wordCount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>目标字数</span>
                  <span className="font-medium">{currentDocument.targetWords.toLocaleString()}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all"
                    style={{ width: `${Math.min((currentDocument.wordCount / currentDocument.targetWords) * 100, 100)}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  完成度: {Math.round((currentDocument.wordCount / currentDocument.targetWords) * 100)}%
                </div>
              </div>
            </div>

            {/* 章节进度 */}
            {analysisResults && (
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3">章节进度</h3>
                <div className="space-y-2">
                  {analysisResults.chapters.map((chapter) => (
                    <div key={chapter.id} className="flex items-center justify-between text-sm">
                      <span className="truncate">{chapter.title}</span>
                      {chapter.status === 'analyzed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                      {chapter.status === 'analyzing' && <Clock className="h-4 w-4 text-yellow-600" />}
                      {chapter.status === 'pending' && <AlertCircle className="h-4 w-4 text-gray-400" />}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* AI建议 */}
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">AI建议</h3>
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-2 border-blue-500">
                  建议增加施工组织设计的详细描述
                </div>
                <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border-l-2 border-yellow-500">
                  质量保证措施需要更多技术细节
                </div>
                <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded border-l-2 border-green-500">
                  安全文明施工方案符合规范要求
                </div>
              </div>
            </div>
          </div>
        </aside>
      </main>
    </div>
  )
}
