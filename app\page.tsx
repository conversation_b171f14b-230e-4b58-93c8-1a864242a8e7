'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Brain, 
  Search, 
  Upload, 
  BarChart3, 
  Settings,
  Plus,
  ArrowRight,
  Sparkles,
  Target,
  Clock,
  Users
} from 'lucide-react'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalDocuments: 0
  })

  useEffect(() => {
    // 模拟加载统计数据
    setStats({
      totalProjects: 12,
      activeProjects: 5,
      completedProjects: 7,
      totalDocuments: 48
    })
  }, [])

  const features = [
    {
      icon: FileText,
      title: '智能文档生成',
      description: '基于AI技术自动生成高质量投标文件，支持多种模板和自定义配置',
      color: 'bg-blue-500'
    },
    {
      icon: Brain,
      title: 'AI分析引擎',
      description: '深度分析招标文件，提取关键信息，生成针对性的投标策略',
      color: 'bg-purple-500'
    },
    {
      icon: Search,
      title: '智能搜索',
      description: '全文搜索和语义搜索，快速找到相关文档和模板内容',
      color: 'bg-green-500'
    },
    {
      icon: Upload,
      title: '文件管理',
      description: '支持多种格式文件上传，版本控制，协作编辑功能',
      color: 'bg-orange-500'
    }
  ]

  const quickActions = [
    {
      title: '创建新项目',
      description: '开始一个新的投标项目',
      icon: Plus,
      href: '/projects/new',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: '上传招标文件',
      description: '上传并分析招标文件',
      icon: Upload,
      href: '/files/upload',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: '浏览模板',
      description: '查看可用的投标模板',
      icon: FileText,
      href: '/templates',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: 'AI分析',
      description: '使用AI分析现有文档',
      icon: Brain,
      href: '/ai/analysis',
      color: 'bg-orange-600 hover:bg-orange-700'
    }
  ]

  const recentProjects = [
    {
      id: '1',
      name: '某市政道路建设项目',
      status: 'in_progress',
      progress: 75,
      deadline: '2024-07-15',
      type: 'construction'
    },
    {
      id: '2',
      name: '办公楼装修工程',
      status: 'review',
      progress: 90,
      deadline: '2024-07-20',
      type: 'construction'
    },
    {
      id: '3',
      name: 'IT设备采购项目',
      status: 'completed',
      progress: 100,
      deadline: '2024-06-30',
      type: 'goods'
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: '草稿', variant: 'secondary' as const },
      in_progress: { label: '进行中', variant: 'default' as const },
      review: { label: '审核中', variant: 'outline' as const },
      completed: { label: '已完成', variant: 'secondary' as const },
      archived: { label: '已归档', variant: 'secondary' as const }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.draft
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* 导航栏 */}
      <nav className="border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Sparkles className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                  投标文件编辑器
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost">仪表板</Button>
              </Link>
              <Link href="/projects">
                <Button variant="ghost">项目管理</Button>
              </Link>
              <Link href="/settings">
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 欢迎区域 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            欢迎使用投标文件编辑器
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            所见即所得的智能投标文件生成系统，让投标更简单、更高效
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="/projects/new">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Plus className="mr-2 h-5 w-5" />
                创建新项目
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button size="lg" variant="outline">
                进入仪表板
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总项目数</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProjects}</div>
              <p className="text-xs text-muted-foreground">
                +2 较上月
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">进行中项目</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeProjects}</div>
              <p className="text-xs text-muted-foreground">
                +1 较上周
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已完成项目</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedProjects}</div>
              <p className="text-xs text-muted-foreground">
                +3 较上月
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">文档总数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDocuments}</div>
              <p className="text-xs text-muted-foreground">
                +12 较上周
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 功能特性 */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              核心功能
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${feature.color}`}>
                        <feature.icon className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* 快速操作 */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              快速操作
            </h2>
            <div className="space-y-4">
              {quickActions.map((action, index) => (
                <Link key={index} href={action.href}>
                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${action.color}`}>
                          <action.icon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-medium">{action.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* 最近项目 */}
        <div className="mt-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              最近项目
            </h2>
            <Link href="/projects">
              <Button variant="outline">
                查看全部
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentProjects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <Badge {...getStatusBadge(project.status)}>
                      {getStatusBadge(project.status).label}
                    </Badge>
                  </div>
                  <CardDescription>
                    截止日期: {project.deadline}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>进度</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Link href={`/projects/${project.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        查看详情
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
