(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[357],{2246:function(e,s,t){Promise.resolve().then(t.bind(t,2322))},2322:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return S}});var a=t(7437),r=t(2265),l=t(5725),n=t(2869),i=t(6070),d=t(5974),c=t(1354),x=t(8736),o=t(3113),m=t(9658),u=t(7586),h=t(1473),p=t(9397),g=t(7689),f=t(1341),j=t(3247),y=t(1281),N=t(740),v=t(4428),b=t(6266),w=t(2369),k=t(1047),z=t(2208),Z=t(2735),C=t(4630),B=t(8930),M=t(6475);function S(){let[e,s]=(0,r.useState)([]),[t,S]=(0,r.useState)(""),[A,R]=(0,r.useState)("all"),[W,Y]=(0,r.useState)("grid"),[E,I]=(0,r.useState)(!0),[O,_]=(0,r.useState)({});(0,r.useEffect)(()=>{setTimeout(()=>{s([{id:"1",name:"技术规范说明书.docx",type:"document",size:"2.5 MB",uploadedAt:"2024-07-02 14:30",uploadedBy:"张工程师",project:"某市政道路建设项目",tags:["技术规范","施工标准"],version:3,status:"ready"},{id:"2",name:"施工现场图片.jpg",type:"image",size:"1.8 MB",uploadedAt:"2024-07-01 16:45",uploadedBy:"李项目经理",project:"办公楼装修工程",tags:["现场照片","进度记录"],version:1,status:"ready"},{id:"3",name:"投标文件模板.pdf",type:"document",size:"5.2 MB",uploadedAt:"2024-06-30 09:15",uploadedBy:"王技术员",project:"IT设备采购项目",tags:["模板","标准格式"],version:2,status:"ready"},{id:"4",name:"设备清单.xlsx",type:"document",size:"890 KB",uploadedAt:"2024-06-29 11:20",uploadedBy:"陈设计师",project:"学校设备采购",tags:["设备清单","采购"],version:1,status:"processing"},{id:"5",name:"合同附件.zip",type:"other",size:"12.3 MB",uploadedAt:"2024-06-28 13:55",uploadedBy:"刘监理",project:"医院设备更新",tags:["合同","附件"],version:1,status:"ready"}]),I(!1)},1e3)},[]);let D=(0,r.useCallback)(e=>{e.forEach(e=>{let t=Date.now().toString()+Math.random().toString(36).substr(2,9);_(e=>({...e,[t]:0}));let a=setInterval(()=>{_(r=>{let l=r[t]||0;if(l>=100){clearInterval(a);let l={id:t,name:e.name,type:e.type.startsWith("image/")?"image":e.type.includes("document")||e.name.endsWith(".pdf")||e.name.endsWith(".docx")||e.name.endsWith(".xlsx")?"document":"other",size:T(e.size),uploadedAt:new Date().toLocaleString("zh-CN"),uploadedBy:"当前用户",tags:[],version:1,status:"ready"};return s(e=>[l,...e]),{...r,[t]:100}}return{...r,[t]:l+10}})},200)})},[]),{getRootProps:F,getInputProps:L,isDragActive:P}=(0,l.uI)({onDrop:D,accept:{"application/pdf":[".pdf"],"application/msword":[".doc"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"application/vnd.ms-excel":[".xls"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"image/*":[".png",".jpg",".jpeg",".gif"],"application/zip":[".zip"],"text/*":[".txt"]}}),T=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},K=e=>{switch(e){case"document":return x.Z;case"image":return o.Z;default:return m.Z}},V=e=>{let s={processing:{color:"bg-yellow-100 text-yellow-800",label:"处理中"},ready:{color:"bg-green-100 text-green-800",label:"就绪"},error:{color:"bg-red-100 text-red-800",label:"错误"}};return s[e]||s.ready},G=e.filter(e=>{let s=e.name.toLowerCase().includes(t.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(t.toLowerCase())),a="all"===A||e.type===A;return s&&a});return E?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"加载文件数据中..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"文件管理"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"管理您的项目文件，支持多种格式上传和版本控制"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.z,{variant:"outline",onClick:()=>Y("grid"===W?"list":"grid"),children:"grid"===W?(0,a.jsx)(u.Z,{className:"h-4 w-4"}):(0,a.jsx)(h.Z,{className:"h-4 w-4"})}),(0,a.jsxs)(n.z,{children:[(0,a.jsx)(p.Z,{className:"mr-2 h-4 w-4"}),"新建文件夹"]})]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)(i.Zb,{className:"mb-8",children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"mr-2 h-5 w-5"}),"文件上传"]}),(0,a.jsx)(i.SZ,{children:"支持拖拽上传，或点击选择文件。支持 PDF、Word、Excel、图片等格式"})]}),(0,a.jsxs)(i.aY,{children:[(0,a.jsxs)("div",{...F(),className:"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ".concat(P?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("input",{...L()}),(0,a.jsx)(f.Z,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),P?(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:"释放文件以开始上传..."}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-2",children:["拖拽文件到此处，或 ",(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:"点击选择文件"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"支持 PDF、Word、Excel、图片、压缩包等格式，单个文件最大 50MB"})]})]}),Object.keys(O).length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-2",children:Object.entries(O).map(e=>{let[s,t]=e;return(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{children:"上传中..."}),(0,a.jsxs)("span",{children:[t,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:"".concat(t,"%")}})})]})},s)})})]})]}),(0,a.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(j.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.I,{placeholder:"搜索文件名或标签...",value:t,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:A,onChange:e=>R(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600",children:[(0,a.jsx)("option",{value:"all",children:"全部类型"}),(0,a.jsx)("option",{value:"document",children:"文档"}),(0,a.jsx)("option",{value:"image",children:"图片"}),(0,a.jsx)("option",{value:"other",children:"其他"})]}),(0,a.jsxs)(n.z,{variant:"outline",children:[(0,a.jsx)(y.Z,{className:"mr-2 h-4 w-4"}),"排序"]}),(0,a.jsxs)(n.z,{variant:"outline",children:[(0,a.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"筛选"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(x.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"文档"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"document"===e.type).length})]})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(o.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"图片"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"image"===e.type).length})]})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(m.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"其他"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"other"===e.type).length})]})]})})}),(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,a.jsx)(v.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"总大小"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"24.7 MB"})]})]})})})]}),"grid"===W?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:G.map(e=>{let s=K(e.type),t=V(e.status);return(0,a.jsxs)(i.Zb,{className:"hover:shadow-lg transition-shadow",children:[(0,a.jsxs)(i.Ol,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-gray-100 rounded-lg",children:(0,a.jsx)(s,{className:"h-6 w-6 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["v",e.version," • ",e.size]})]})]}),(0,a.jsx)(n.z,{variant:"ghost",size:"icon",children:(0,a.jsx)(b.Z,{className:"h-4 w-4"})})]}),(0,a.jsx)(d.C,{className:t.color,children:t.label})]}),(0,a.jsx)(i.aY,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(w.Z,{className:"mr-1 h-3 w-3"}),e.uploadedBy]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(k.Z,{className:"mr-1 h-3 w-3"}),e.uploadedAt]})]}),e.project&&(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"项目："}),(0,a.jsx)("span",{className:"text-blue-600",children:e.project})]}),e.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>(0,a.jsx)(d.C,{variant:"secondary",className:"text-xs",children:e},s))}),(0,a.jsxs)("div",{className:"flex space-x-1 pt-2",children:[(0,a.jsxs)(n.z,{variant:"outline",size:"sm",className:"flex-1",children:[(0,a.jsx)(z.Z,{className:"mr-1 h-3 w-3"}),"查看"]}),(0,a.jsx)(n.z,{variant:"outline",size:"sm",children:(0,a.jsx)(Z.Z,{className:"h-3 w-3"})}),(0,a.jsx)(n.z,{variant:"outline",size:"sm",children:(0,a.jsx)(C.Z,{className:"h-3 w-3"})})]})]})})]},e.id)})}):(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"文件名"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"大小"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"上传者"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"上传时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:G.map(e=>{let s=K(e.type),t=V(e.status);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(s,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["版本 ",e.version]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.size}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uploadedBy}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uploadedAt}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)(d.C,{className:t.color,children:t.label})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(z.Z,{className:"h-4 w-4"})}),(0,a.jsx)(n.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(Z.Z,{className:"h-4 w-4"})}),(0,a.jsx)(n.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(C.Z,{className:"h-4 w-4"})}),(0,a.jsx)(n.z,{variant:"ghost",size:"sm",children:(0,a.jsx)(B.Z,{className:"h-4 w-4"})})]})})]},e.id)})})]})})})}),0===G.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(M.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"没有找到文件"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"尝试调整搜索条件或上传新文件"})]})]})]})}},5974:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var a=t(7437);t(2265);var r=t(535),l=t(9554);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},2869:function(e,s,t){"use strict";t.d(s,{z:function(){return c}});var a=t(7437),r=t(2265),l=t(7053),n=t(535),i=t(9554);let d=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:c=!1,...x}=e,o=c?l.g7:"button";return(0,a.jsx)(o,{className:(0,i.cn)(d({variant:r,size:n,className:t})),ref:s,...x})});c.displayName="Button"},6070:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return n},aY:function(){return x},ll:function(){return d}});var a=t(7437),r=t(2265),l=t(9554);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});x.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},1354:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var a=t(7437),r=t(2265),l=t(1994),n=t(3335);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},9554:function(e,s,t){"use strict";t.d(s,{cn:function(){return l}});var a=t(1994),r=t(3335);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,a.W)(s))}}},function(e){e.O(0,[635,875,971,117,744],function(){return e(e.s=2246)}),_N_E=e.O()}]);