"use strict";(()=>{var e={};e.id=887,e.ids=[887],e.modules={8013:e=>{e.exports=require("mongodb")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5315:e=>{e.exports=require("path")},2885:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>y,patchFetch:()=>h,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>x,staticGenerationAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{GET:()=>d,POST:()=>m});var a=s(9303),i=s(8716),o=s(670),n=s(7070),p=s(2021);let c=require("fs/promises");var u=s(5315),l=s(8013);async function d(e){try{let{db:t}=await (0,p.vO)(),{searchParams:s}=new URL(e.url),r=parseInt(s.get("page")||"1"),a=parseInt(s.get("limit")||"20"),i=s.get("type"),o=s.get("search"),c=s.get("projectId"),u={};i&&"all"!==i&&(u.type=i),c&&(u.projectId=c),o&&(u.$or=[{name:{$regex:o,$options:"i"}},{tags:{$in:[RegExp(o,"i")]}}]);let l=(r-1)*a,d=await t.collection("files").find(u).sort({uploadedAt:-1}).skip(l).limit(a).toArray(),m=await t.collection("files").countDocuments(u);return n.NextResponse.json({success:!0,data:{files:d,pagination:{page:r,limit:a,total:m,pages:Math.ceil(m/a)}}})}catch(e){return console.error("获取文件列表失败:",e),n.NextResponse.json({success:!1,error:"获取文件列表失败"},{status:500})}}async function m(e){try{let{db:t}=await (0,p.vO)(),s=await e.formData(),r=s.get("file"),a=s.get("projectId"),i=s.get("tags"),o=s.get("description");if(!r)return n.NextResponse.json({success:!1,error:"没有上传文件"},{status:400});if(r.size>52428800)return n.NextResponse.json({success:!1,error:"文件大小超过50MB限制"},{status:400});if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png","image/gif","text/plain","application/zip"].includes(r.type))return n.NextResponse.json({success:!1,error:"不支持的文件类型"},{status:400});let d=Date.now(),m=Math.random().toString(36).substring(2,15),g=r.name.split(".").pop(),f=`${d}_${m}.${g}`,w=(0,u.join)(process.cwd(),"uploads");try{await (0,c.mkdir)(w,{recursive:!0})}catch(e){}let x=(0,u.join)(w,f),y=await r.arrayBuffer(),h=Buffer.from(y);await (0,c.writeFile)(x,h);let j="other";r.type.startsWith("image/")?j="image":(r.type.includes("pdf")||r.type.includes("document")||r.type.includes("word")||r.type.includes("excel")||r.type.includes("sheet"))&&(j="document");let v={name:r.name,originalName:r.name,fileName:f,type:j,mimeType:r.type,size:r.size,path:`/uploads/${f}`,projectId:a||null,tags:i?i.split(",").map(e=>e.trim()):[],description:o||"",version:1,status:"ready",uploadedAt:new Date,uploadedBy:"current-user",downloads:0,lastAccessed:new Date},R=await t.collection("files").insertOne(v);return a&&l.ObjectId.isValid(a)&&await t.collection("projects").updateOne({_id:new l.ObjectId(a)},{$inc:{documentCount:1},$set:{updatedAt:new Date}}),n.NextResponse.json({success:!0,data:{id:R.insertedId,...v}},{status:201})}catch(e){return console.error("文件上传失败:",e),n.NextResponse.json({success:!1,error:"文件上传失败"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/files/route",pathname:"/api/files",filename:"route",bundlePath:"app/api/files/route"},resolvedPagePath:"E:\\rjkf\\tb-0704-V\\app\\api\\files\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:x}=g,y="/api/files/route";function h(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:w})}},2021:(e,t,s)=>{let r;async function a(){let e=await r,t=e.db("tender-editor");return{client:e,db:t}}s.d(t,{vO:()=>a}),r=new(s(8013)).MongoClient("mongodb://localhost:27017/tender-editor",{}).connect()}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,972],()=>s(2885));module.exports=r})();