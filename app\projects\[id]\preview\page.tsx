'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Download, 
  Share, 
  Printer,
  Eye,
  FileText,
  Image,
  BarChart3,
  Calendar,
  User,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  Zap,
  Target,
  TrendingUp,
  PieChart,
  Activity
} from 'lucide-react'

interface ProjectPreview {
  id: string
  name: string
  description: string
  status: 'planning' | 'in_progress' | 'review' | 'completed'
  progress: number
  startDate: string
  endDate: string
  budget: string
  client: string
  manager: string
  team: string[]
  documents: {
    id: string
    name: string
    type: string
    size: string
    status: string
    lastModified: string
  }[]
  milestones: {
    id: string
    name: string
    date: string
    status: 'completed' | 'in_progress' | 'pending'
    description: string
  }[]
  aiAnalyses: {
    id: string
    type: string
    title: string
    score: number
    date: string
    status: string
  }[]
  statistics: {
    totalDocuments: number
    completedTasks: number
    aiAnalysesCount: number
    qualityScore: number
    complianceRate: number
    efficiency: number
  }
}

export default function ProjectPreviewPage() {
  const params = useParams()
  const projectId = params.id as string
  
  const [project, setProject] = useState<ProjectPreview | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    // 模拟加载项目数据
    setTimeout(() => {
      setProject({
        id: projectId,
        name: '某市政道路建设项目',
        description: '城市主干道改造升级工程，包括道路拓宽、排水系统改造、绿化景观提升等内容',
        status: 'in_progress',
        progress: 68,
        startDate: '2024-03-15',
        endDate: '2024-12-30',
        budget: '2,500万元',
        client: '某市城建局',
        manager: '张工程师',
        team: ['李技术员', '王质检员', '赵安全员', '陈资料员'],
        documents: [
          {
            id: '1',
            name: '技术方案书.docx',
            type: 'document',
            size: '2.3 MB',
            status: 'completed',
            lastModified: '2024-07-03 14:30'
          },
          {
            id: '2',
            name: '施工组织设计.pdf',
            type: 'document',
            size: '5.7 MB',
            status: 'review',
            lastModified: '2024-07-02 16:45'
          },
          {
            id: '3',
            name: '工程量清单.xlsx',
            type: 'spreadsheet',
            size: '1.2 MB',
            status: 'completed',
            lastModified: '2024-07-01 09:15'
          }
        ],
        milestones: [
          {
            id: '1',
            name: '方案设计完成',
            date: '2024-04-15',
            status: 'completed',
            description: '完成初步设计和详细设计'
          },
          {
            id: '2',
            name: '招标文件准备',
            date: '2024-05-30',
            status: 'completed',
            description: '完成招标文件编制和审核'
          },
          {
            id: '3',
            name: '施工准备',
            date: '2024-07-15',
            status: 'in_progress',
            description: '现场准备和材料采购'
          },
          {
            id: '4',
            name: '主体施工',
            date: '2024-10-30',
            status: 'pending',
            description: '道路主体工程施工'
          }
        ],
        aiAnalyses: [
          {
            id: '1',
            type: 'document_analysis',
            title: '技术方案合规性分析',
            score: 92,
            date: '2024-07-03',
            status: 'completed'
          },
          {
            id: '2',
            type: 'risk_assessment',
            title: '项目风险评估',
            score: 85,
            date: '2024-07-02',
            status: 'completed'
          },
          {
            id: '3',
            type: 'quality_check',
            title: '文档质量检查',
            score: 88,
            date: '2024-07-01',
            status: 'completed'
          }
        ],
        statistics: {
          totalDocuments: 15,
          completedTasks: 12,
          aiAnalysesCount: 8,
          qualityScore: 88,
          complianceRate: 95,
          efficiency: 82
        }
      })
      setLoading(false)
    }, 1000)
  }, [projectId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载项目数据中...</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">项目不存在或加载失败</p>
        </div>
      </div>
    )
  }

  const getStatusInfo = (status: string) => {
    const statusMap = {
      planning: { label: '规划中', color: 'bg-gray-100 text-gray-800', icon: Calendar },
      in_progress: { label: '进行中', color: 'bg-blue-100 text-blue-800', icon: Activity },
      review: { label: '审核中', color: 'bg-yellow-100 text-yellow-800', icon: Eye },
      completed: { label: '已完成', color: 'bg-green-100 text-green-800', icon: CheckCircle }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.planning
  }

  const statusInfo = getStatusInfo(project.status)
  const StatusIcon = statusInfo.icon

  const tabs = [
    { id: 'overview', name: '项目概览', icon: BarChart3 },
    { id: 'documents', name: '文档管理', icon: FileText },
    { id: 'milestones', name: '里程碑', icon: Target },
    { id: 'ai_analysis', name: 'AI分析', icon: Zap },
    { id: 'statistics', name: '统计报告', icon: PieChart }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回项目列表
              </Button>
              
              <div className="flex items-center space-x-3">
                <StatusIcon className="h-6 w-6 text-blue-600" />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {project.name}
                  </h1>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Badge className={statusInfo.color}>
                      {statusInfo.label}
                    </Badge>
                    <span>进度: {project.progress}%</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Printer className="mr-2 h-4 w-4" />
                打印
              </Button>
              <Button variant="outline" size="sm">
                <Share className="mr-2 h-4 w-4" />
                分享
              </Button>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const TabIcon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <TabIcon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* 项目基本信息 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>项目信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">项目名称</label>
                        <p className="text-gray-900 dark:text-white">{project.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">项目状态</label>
                        <Badge className={statusInfo.color}>{statusInfo.label}</Badge>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">开始日期</label>
                        <p className="text-gray-900 dark:text-white">{project.startDate}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">结束日期</label>
                        <p className="text-gray-900 dark:text-white">{project.endDate}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">项目预算</label>
                        <p className="text-gray-900 dark:text-white">{project.budget}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">委托方</label>
                        <p className="text-gray-900 dark:text-white">{project.client}</p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">项目描述</label>
                      <p className="text-gray-900 dark:text-white mt-1">{project.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                {/* 进度卡片 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="mr-2 h-5 w-5" />
                      项目进度
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span>整体进度</span>
                        <span className="font-medium">{project.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 团队信息 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <User className="mr-2 h-5 w-5" />
                      项目团队
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">项目经理</label>
                        <p className="text-gray-900 dark:text-white">{project.manager}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">团队成员</label>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {project.team.map((member, index) => (
                            <Badge key={index} variant="secondary">{member}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}

        {/* 文档管理标签页 */}
        {activeTab === 'documents' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  项目文档
                </CardTitle>
                <CardDescription>
                  查看和管理项目相关的所有文档
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-8 w-8 text-blue-600" />
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">{doc.name}</h4>
                          <p className="text-sm text-gray-500">
                            {doc.size} • 最后修改: {doc.lastModified}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className={
                          doc.status === 'completed' ? 'bg-green-100 text-green-800' :
                          doc.status === 'review' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {doc.status === 'completed' ? '已完成' :
                           doc.status === 'review' ? '审核中' : '草稿'}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Eye className="mr-1 h-3 w-3" />
                          查看
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="mr-1 h-3 w-3" />
                          下载
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 里程碑标签页 */}
        {activeTab === 'milestones' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="mr-2 h-5 w-5" />
                  项目里程碑
                </CardTitle>
                <CardDescription>
                  跟踪项目关键节点和进度
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {project.milestones.map((milestone, index) => (
                    <div key={milestone.id} className="relative">
                      {index !== project.milestones.length - 1 && (
                        <div className="absolute left-4 top-8 w-0.5 h-16 bg-gray-200 dark:bg-gray-700"></div>
                      )}
                      <div className="flex items-start space-x-4">
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          milestone.status === 'completed' ? 'bg-green-100 text-green-600' :
                          milestone.status === 'in_progress' ? 'bg-blue-100 text-blue-600' :
                          'bg-gray-100 text-gray-400'
                        }`}>
                          {milestone.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : milestone.status === 'in_progress' ? (
                            <Clock className="h-4 w-4" />
                          ) : (
                            <Calendar className="h-4 w-4" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {milestone.name}
                            </h4>
                            <span className="text-sm text-gray-500">{milestone.date}</span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {milestone.description}
                          </p>
                          <Badge className={`mt-2 ${
                            milestone.status === 'completed' ? 'bg-green-100 text-green-800' :
                            milestone.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {milestone.status === 'completed' ? '已完成' :
                             milestone.status === 'in_progress' ? '进行中' : '待开始'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* AI分析标签页 */}
        {activeTab === 'ai_analysis' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="mr-2 h-5 w-5" />
                  AI分析报告
                </CardTitle>
                <CardDescription>
                  查看AI对项目文档的分析结果
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {project.aiAnalyses.map((analysis) => (
                    <Card key={analysis.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <Badge variant="secondary">
                            {analysis.type === 'document_analysis' ? '文档分析' :
                             analysis.type === 'risk_assessment' ? '风险评估' :
                             analysis.type === 'quality_check' ? '质量检查' : '其他'}
                          </Badge>
                          <span className="text-sm text-gray-500">{analysis.date}</span>
                        </div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                          {analysis.title}
                        </h4>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">评分:</span>
                            <span className={`font-medium ${
                              analysis.score >= 90 ? 'text-green-600' :
                              analysis.score >= 80 ? 'text-blue-600' :
                              analysis.score >= 70 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {analysis.score}分
                            </span>
                          </div>
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 统计报告标签页 */}
        {activeTab === 'statistics' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">文档总数</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.totalDocuments}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">完成任务</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.completedTasks}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Zap className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">AI分析</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.aiAnalysesCount}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Star className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">质量评分</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.qualityScore}分
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <AlertCircle className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">合规率</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.complianceRate}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">效率指数</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {project.statistics.efficiency}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
