'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Brain, 
  FileText, 
  Sparkles, 
  Search,
  Upload,
  Download,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  AlertCircle,
  Clock,
  Lightbulb,
  Target,
  TrendingUp,
  BarChart3,
  Eye,
  Edit,
  Share,
  Bookmark
} from 'lucide-react'

interface AnalysisResult {
  id: string
  title: string
  type: 'document_analysis' | 'content_generation' | 'optimization' | 'compliance_check'
  status: 'running' | 'completed' | 'failed' | 'pending'
  progress: number
  createdAt: string
  completedAt?: string
  inputFile?: string
  results?: {
    summary?: string
    keyPoints?: string[]
    suggestions?: string[]
    score?: number
    issues?: string[]
    improvements?: string[]
  }
}

export default function AIPage() {
  const [analyses, setAnalyses] = useState<AnalysisResult[]>([])
  const [selectedAnalysis, setSelectedAnalysis] = useState<AnalysisResult | null>(null)
  const [inputText, setInputText] = useState('')
  const [analysisType, setAnalysisType] = useState('document_analysis')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // 模拟加载分析历史
    setAnalyses([
      {
        id: '1',
        title: '技术规范说明书分析',
        type: 'document_analysis',
        status: 'completed',
        progress: 100,
        createdAt: '2024-07-02 14:30',
        completedAt: '2024-07-02 14:35',
        inputFile: '技术规范说明书.docx',
        results: {
          summary: '该技术规范说明书内容完整，涵盖了施工标准、材料要求、质量控制等关键要素。',
          keyPoints: [
            '施工工艺标准明确',
            '材料规格要求详细',
            '质量验收标准完善',
            '安全措施规定充分'
          ],
          suggestions: [
            '建议增加环保要求说明',
            '可以补充施工进度安排',
            '建议添加应急预案内容'
          ],
          score: 85
        }
      },
      {
        id: '2',
        title: '投标文件合规性检查',
        type: 'compliance_check',
        status: 'completed',
        progress: 100,
        createdAt: '2024-07-01 16:45',
        completedAt: '2024-07-01 16:50',
        inputFile: '投标文件模板.pdf',
        results: {
          score: 92,
          issues: [
            '缺少企业资质证明附件',
            '财务报表格式不规范'
          ],
          improvements: [
            '补充完整的企业资质文件',
            '按照标准格式整理财务报表',
            '增加项目经验证明材料'
          ]
        }
      },
      {
        id: '3',
        title: '投标书内容优化',
        type: 'optimization',
        status: 'running',
        progress: 65,
        createdAt: '2024-07-02 10:20',
        inputFile: '投标书草稿.docx'
      },
      {
        id: '4',
        title: '技术方案生成',
        type: 'content_generation',
        status: 'pending',
        progress: 0,
        createdAt: '2024-07-02 15:00'
      }
    ])
  }, [])

  const getAnalysisTypeInfo = (type: string) => {
    const typeMap = {
      document_analysis: { 
        label: '文档分析', 
        color: 'bg-blue-100 text-blue-800',
        icon: FileText,
        description: '分析文档内容，提取关键信息和要点'
      },
      content_generation: { 
        label: '内容生成', 
        color: 'bg-green-100 text-green-800',
        icon: Sparkles,
        description: '基于需求自动生成投标文件内容'
      },
      optimization: { 
        label: '内容优化', 
        color: 'bg-purple-100 text-purple-800',
        icon: TrendingUp,
        description: '优化现有内容，提升质量和竞争力'
      },
      compliance_check: { 
        label: '合规检查', 
        color: 'bg-orange-100 text-orange-800',
        icon: CheckCircle,
        description: '检查文档是否符合招标要求和规范'
      }
    }
    return typeMap[type as keyof typeof typeMap] || typeMap.document_analysis
  }

  const getStatusInfo = (status: string) => {
    const statusMap = {
      running: { color: 'bg-blue-100 text-blue-800', label: '运行中', icon: Play },
      completed: { color: 'bg-green-100 text-green-800', label: '已完成', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', label: '失败', icon: AlertCircle },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: '等待中', icon: Clock }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.pending
  }

  const startAnalysis = async () => {
    if (!inputText.trim()) return

    setLoading(true)
    const newAnalysis: AnalysisResult = {
      id: Date.now().toString(),
      title: `${getAnalysisTypeInfo(analysisType).label} - ${new Date().toLocaleString()}`,
      type: analysisType as any,
      status: 'running',
      progress: 0,
      createdAt: new Date().toLocaleString('zh-CN')
    }

    setAnalyses(prev => [newAnalysis, ...prev])
    setSelectedAnalysis(newAnalysis)

    // 模拟分析进度
    const interval = setInterval(() => {
      setAnalyses(prev => prev.map(analysis => {
        if (analysis.id === newAnalysis.id) {
          const newProgress = Math.min(analysis.progress + 20, 100)
          if (newProgress === 100) {
            clearInterval(interval)
            setLoading(false)
            return {
              ...analysis,
              status: 'completed',
              progress: 100,
              completedAt: new Date().toLocaleString('zh-CN'),
              results: {
                summary: '分析完成，内容质量良好，建议进行部分优化。',
                keyPoints: ['结构清晰', '内容完整', '格式规范'],
                suggestions: ['增加具体案例', '优化语言表达', '补充技术细节'],
                score: 88
              }
            }
          }
          return { ...analysis, progress: newProgress }
        }
        return analysis
      }))
    }, 1000)

    setInputText('')
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <Brain className="mr-3 h-8 w-8 text-blue-600" />
                AI智能分析
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                利用人工智能技术分析文档、生成内容、优化投标文件
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：新建分析 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Sparkles className="mr-2 h-5 w-5" />
                  新建分析
                </CardTitle>
                <CardDescription>
                  选择分析类型并输入内容开始AI分析
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">分析类型</label>
                  <select
                    value={analysisType}
                    onChange={(e) => setAnalysisType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
                  >
                    <option value="document_analysis">文档分析</option>
                    <option value="content_generation">内容生成</option>
                    <option value="optimization">内容优化</option>
                    <option value="compliance_check">合规检查</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    {getAnalysisTypeInfo(analysisType).description}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">输入内容</label>
                  <Textarea
                    placeholder="请输入要分析的文本内容，或描述您的需求..."
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    rows={8}
                    className="resize-none"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button 
                    onClick={startAnalysis} 
                    disabled={!inputText.trim() || loading}
                    className="flex-1"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        分析中...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        开始分析
                      </>
                    )}
                  </Button>
                  <Button variant="outline">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>

                {/* 快速模板 */}
                <div className="border-t pt-4">
                  <h4 className="text-sm font-medium mb-3">快速模板</h4>
                  <div className="space-y-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start text-xs"
                      onClick={() => setInputText('请分析这份技术规范文档的完整性和合规性...')}
                    >
                      <FileText className="mr-2 h-3 w-3" />
                      技术规范分析
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start text-xs"
                      onClick={() => setInputText('请为道路建设项目生成技术方案...')}
                    >
                      <Sparkles className="mr-2 h-3 w-3" />
                      技术方案生成
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start text-xs"
                      onClick={() => setInputText('请检查投标文件是否符合招标要求...')}
                    >
                      <CheckCircle className="mr-2 h-3 w-3" />
                      合规性检查
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* AI功能介绍 */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">AI功能特色</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-blue-100 rounded">
                    <Brain className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">智能分析</p>
                    <p className="text-xs text-gray-500">深度理解文档内容和结构</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-green-100 rounded">
                    <Lightbulb className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">智能建议</p>
                    <p className="text-xs text-gray-500">提供专业的优化建议</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-purple-100 rounded">
                    <Target className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">精准评分</p>
                    <p className="text-xs text-gray-500">量化评估文档质量</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：分析历史和结果 */}
          <div className="lg:col-span-2">
            {/* 分析历史 */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    分析历史
                  </span>
                  <Button variant="outline" size="sm">
                    <Search className="mr-2 h-4 w-4" />
                    搜索
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyses.map((analysis) => {
                    const typeInfo = getAnalysisTypeInfo(analysis.type)
                    const statusInfo = getStatusInfo(analysis.status)
                    const TypeIcon = typeInfo.icon
                    const StatusIcon = statusInfo.icon

                    return (
                      <div 
                        key={analysis.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedAnalysis?.id === analysis.id 
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedAnalysis(analysis)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <div className="p-2 bg-gray-100 rounded-lg">
                              <TypeIcon className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                                {analysis.title}
                              </h4>
                              <p className="text-xs text-gray-500 mt-1">
                                {analysis.inputFile && `文件: ${analysis.inputFile} • `}
                                创建于 {analysis.createdAt}
                              </p>
                              <div className="flex items-center space-x-2 mt-2">
                                <Badge className={typeInfo.color}>
                                  {typeInfo.label}
                                </Badge>
                                <Badge className={statusInfo.color}>
                                  <StatusIcon className="mr-1 h-3 w-3" />
                                  {statusInfo.label}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              {analysis.progress}%
                            </div>
                            <div className="w-16 bg-gray-200 rounded-full h-1.5 mt-1">
                              <div 
                                className="bg-blue-500 h-1.5 rounded-full transition-all"
                                style={{ width: `${analysis.progress}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* 分析结果详情 */}
            {selectedAnalysis && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <Eye className="mr-2 h-5 w-5" />
                      分析结果
                    </CardTitle>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        导出
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share className="mr-2 h-4 w-4" />
                        分享
                      </Button>
                      <Button variant="outline" size="sm">
                        <Bookmark className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardDescription>
                    {selectedAnalysis.title} • {selectedAnalysis.createdAt}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {selectedAnalysis.status === 'completed' && selectedAnalysis.results ? (
                    <div className="space-y-6">
                      {/* 评分 */}
                      {selectedAnalysis.results.score && (
                        <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-3xl font-bold text-blue-600 mb-2">
                            {selectedAnalysis.results.score}
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300">综合评分</p>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                            <div 
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${selectedAnalysis.results.score}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* 摘要 */}
                      {selectedAnalysis.results.summary && (
                        <div>
                          <h4 className="text-lg font-medium mb-3">分析摘要</h4>
                          <p className="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                            {selectedAnalysis.results.summary}
                          </p>
                        </div>
                      )}

                      {/* 关键要点 */}
                      {selectedAnalysis.results.keyPoints && (
                        <div>
                          <h4 className="text-lg font-medium mb-3">关键要点</h4>
                          <div className="space-y-2">
                            {selectedAnalysis.results.keyPoints.map((point, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{point}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 优化建议 */}
                      {selectedAnalysis.results.suggestions && (
                        <div>
                          <h4 className="text-lg font-medium mb-3">优化建议</h4>
                          <div className="space-y-2">
                            {selectedAnalysis.results.suggestions.map((suggestion, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{suggestion}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 问题和改进 */}
                      {selectedAnalysis.results.issues && (
                        <div>
                          <h4 className="text-lg font-medium mb-3">发现的问题</h4>
                          <div className="space-y-2">
                            {selectedAnalysis.results.issues.map((issue, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{issue}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedAnalysis.results.improvements && (
                        <div>
                          <h4 className="text-lg font-medium mb-3">改进建议</h4>
                          <div className="space-y-2">
                            {selectedAnalysis.results.improvements.map((improvement, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700 dark:text-gray-300">{improvement}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : selectedAnalysis.status === 'running' ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600 dark:text-gray-300">AI正在分析中，请稍候...</p>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-4">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${selectedAnalysis.progress}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        进度: {selectedAnalysis.progress}%
                      </p>
                    </div>
                  ) : selectedAnalysis.status === 'failed' ? (
                    <div className="text-center py-8">
                      <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-300">分析失败，请重试</p>
                      <Button className="mt-4">
                        <RotateCcw className="mr-2 h-4 w-4" />
                        重新分析
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Clock className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-300">等待分析开始...</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
