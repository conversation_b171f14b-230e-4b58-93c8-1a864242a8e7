(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[219],{1328:function(e,s,t){Promise.resolve().then(t.bind(t,9224))},9224:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return F}});var r=t(7437),a=t(2265),n=t(2869),l=t(6070),i=t(1354),c=t(5974),d=t(8348),o=t(9680),m=t(7494),x=t(1734),u=t(8310),h=t(7881),f=t(7586),j=t(8645),g=t(8335),p=t(3113),b=t(5965),v=t(4353),N=t(8736),y=t(7689),w=t(2735),z=t(3229),Z=t(2805);function C(e){let{initialContent:s="",onSave:t,onContentChange:i,placeholder:c="开始编写您的投标文档...",readOnly:C=!1}=e,[k,S]=(0,a.useState)(s),[O,R]=(0,a.useState)(!1),A=(0,a.useRef)(null);(0,a.useEffect)(()=>{A.current&&s&&(A.current.innerHTML=s)},[s]);let I=(e,s)=>{document.execCommand(e,!1,s),L()},L=()=>{if(A.current){let e=A.current.innerHTML;S(e),null==i||i(e)}},Y=e=>{I("fontSize",e)},E=e=>{I("foreColor",e)},_=[{group:"格式",buttons:[{icon:d.Z,command:"bold",title:"粗体"},{icon:o.Z,command:"italic",title:"斜体"},{icon:m.Z,command:"underline",title:"下划线"}]},{group:"对齐",buttons:[{icon:x.Z,command:"justifyLeft",title:"左对齐"},{icon:u.Z,command:"justifyCenter",title:"居中对齐"},{icon:h.Z,command:"justifyRight",title:"右对齐"}]},{group:"列表",buttons:[{icon:f.Z,command:"insertUnorderedList",title:"无序列表"},{icon:j.Z,command:"insertOrderedList",title:"有序列表"}]},{group:"插入",buttons:[{icon:g.Z,action:()=>{let e=prompt("请输入链接URL:");e&&I("createLink",e)},title:"插入链接"},{icon:p.Z,action:()=>{let e=prompt("请输入图片URL:");e&&I("insertImage",e)},title:"插入图片"}]},{group:"操作",buttons:[{icon:b.Z,command:"undo",title:"撤销"},{icon:v.Z,command:"redo",title:"重做"}]}];return(0,r.jsxs)(l.Zb,{className:"w-full",children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(l.ll,{className:"flex items-center",children:[(0,r.jsx)(N.Z,{className:"mr-2 h-5 w-5"}),"文档编辑器"]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:!C&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(y.Z,{className:"mr-1 h-3 w-3"}),"导入"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(w.Z,{className:"mr-1 h-3 w-3"}),"导出"]}),(0,r.jsxs)(n.z,{onClick:()=>{null==t||t(k),R(!1)},size:"sm",children:[(0,r.jsx)(z.Z,{className:"mr-1 h-3 w-3"}),"保存"]})]})})]})}),!C&&(0,r.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 px-6 py-3",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[_.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[e.buttons.map((e,s)=>{let t=e.icon;return(0,r.jsx)(n.z,{variant:"ghost",size:"sm",onClick:()=>{e.command?I(e.command):e.action&&e.action()},title:e.title,className:"h-8 w-8 p-0",children:(0,r.jsx)(t,{className:"h-4 w-4"})},s)}),s<_.length-1&&(0,r.jsx)("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"})]},s)),(0,r.jsxs)("select",{onChange:e=>Y(e.target.value),className:"px-2 py-1 border border-gray-300 rounded text-sm bg-white dark:bg-gray-800 dark:border-gray-600",children:[(0,r.jsx)("option",{value:"1",children:"小"}),(0,r.jsx)("option",{value:"3",selected:!0,children:"正常"}),(0,r.jsx)("option",{value:"5",children:"大"}),(0,r.jsx)("option",{value:"7",children:"特大"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(Z.Z,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)("input",{type:"color",onChange:e=>E(e.target.value),className:"w-8 h-8 border border-gray-300 rounded cursor-pointer",title:"文字颜色"})]})]})}),(0,r.jsx)(l.aY,{className:"p-0",children:(0,r.jsx)("div",{ref:A,contentEditable:!C,onInput:L,onFocus:()=>R(!0),className:"min-h-[400px] p-6 focus:outline-none ".concat(C?"cursor-default":"cursor-text"),style:{lineHeight:"1.6",fontSize:"16px"},suppressContentEditableWarning:!0,children:!k&&!C&&(0,r.jsx)("div",{className:"text-gray-400 pointer-events-none",children:c})})}),(0,r.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 px-6 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{children:["字数: ",k.replace(/<[^>]*>/g,"").length]}),(0,r.jsxs)("span",{children:["段落: ",(k.match(/<p>/g)||[]).length||1]}),O&&(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:"正在编辑..."})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{children:"最后保存: 刚刚"})})]})})]})}var k=t(2660),S=t(4972),O=t(2208),R=t(4394),A=t(6266),I=t(2720),L=t(4924),Y=t(1239),E=t(6595),_=t(8728),D=t(1723);function F(){let[e,s]=(0,a.useState)({id:"1",title:"新建投标文档",content:"",tags:["投标文件","技术方案"],status:"draft",createdAt:"2024-07-04 10:30",updatedAt:"2024-07-04 10:30",createdBy:"当前用户",version:1,wordCount:0}),[t,d]=(0,a.useState)(!0),[o,m]=(0,a.useState)(!1),[x,u]=(0,a.useState)(new Date),h=async e=>{m(!0);try{await new Promise(e=>setTimeout(e,1e3)),s(s=>({...s,content:e,updatedAt:new Date().toISOString()})),u(new Date),d(!1)}catch(e){console.error("保存失败:",e)}finally{m(!1)}},f=e=>{s(s=>({...s,title:e}))},j=(e=>{let s={draft:{label:"草稿",color:"bg-gray-100 text-gray-800"},review:{label:"审核中",color:"bg-yellow-100 text-yellow-800"},approved:{label:"已批准",color:"bg-green-100 text-green-800"},published:{label:"已发布",color:"bg-blue-100 text-blue-800"}};return s[e]||s.draft})(e.status);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n.z,{variant:"ghost",size:"sm",children:[(0,r.jsx)(k.Z,{className:"mr-2 h-4 w-4"}),"返回"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(N.Z,{className:"h-6 w-6 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)(i.I,{value:e.title,onChange:e=>f(e.target.value),className:"text-lg font-medium border-none p-0 h-auto bg-transparent focus:ring-0",placeholder:"文档标题"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["版本 ",e.version]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:[e.wordCount," 字"]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)(c.C,{className:j.color,children:j.label})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(S.Z,{className:"mr-2 h-4 w-4"}),"AI助手"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(O.Z,{className:"mr-2 h-4 w-4"}),"预览"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(R.Z,{className:"mr-2 h-4 w-4"}),"分享"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"导出"]}),(0,r.jsx)(n.z,{onClick:()=>h(e.content),disabled:o,size:"sm",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"保存中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(z.Z,{className:"mr-2 h-4 w-4"}),"保存"]})}),(0,r.jsx)(n.z,{variant:"ghost",size:"sm",children:(0,r.jsx)(A.Z,{className:"h-4 w-4"})})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)(C,{initialContent:e.content,onSave:h,onContentChange:e=>{s(s=>({...s,content:e,wordCount:e.replace(/<[^>]*>/g,"").length,updatedAt:new Date().toISOString()}))},placeholder:"开始编写您的投标文档内容..."})}),(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsx)(l.ll,{className:"text-lg",children:"文档信息"})}),(0,r.jsxs)(l.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"创建时间"}),(0,r.jsx)("span",{children:e.createdAt})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"更新时间"}),(0,r.jsx)("span",{children:e.updatedAt})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"创建者"}),(0,r.jsx)("span",{children:e.createdBy})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"字数统计"}),(0,r.jsx)("span",{children:e.wordCount})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"最后保存"}),(0,r.jsx)("span",{children:x.toLocaleTimeString()})]})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)(l.ll,{className:"text-lg flex items-center",children:[(0,r.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),"标签"]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-3",children:e.tags.map((e,s)=>(0,r.jsx)(c.C,{variant:"secondary",children:e},s))}),(0,r.jsx)(n.z,{variant:"outline",size:"sm",className:"w-full",children:"添加标签"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)(l.ll,{className:"text-lg flex items-center",children:[(0,r.jsx)(L.Z,{className:"mr-2 h-4 w-4"}),"项目关联"]})}),(0,r.jsx)(l.aY,{children:e.projectName?(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm",children:e.projectName}),(0,r.jsx)(n.z,{variant:"ghost",size:"sm",children:"更改"})]}):(0,r.jsx)(n.z,{variant:"outline",size:"sm",className:"w-full",children:"关联项目"})})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"pb-3",children:[(0,r.jsxs)(l.ll,{className:"text-lg flex items-center",children:[(0,r.jsx)(Y.Z,{className:"mr-2 h-4 w-4"}),"AI助手"]}),(0,r.jsx)(l.SZ,{children:"使用AI功能提升文档质量"})]}),(0,r.jsxs)(l.aY,{className:"space-y-2",children:[(0,r.jsxs)(n.z,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,r.jsx)(S.Z,{className:"mr-2 h-4 w-4"}),"内容优化"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,r.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"语法检查"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,r.jsx)(E.Z,{className:"mr-2 h-4 w-4"}),"质量评估"]}),(0,r.jsxs)(n.z,{variant:"outline",size:"sm",className:"w-full justify-start",children:[(0,r.jsx)(_.Z,{className:"mr-2 h-4 w-4"}),"合规检查"]})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{className:"pb-3",children:(0,r.jsxs)(l.ll,{className:"text-lg flex items-center",children:[(0,r.jsx)(D.Z,{className:"mr-2 h-4 w-4"}),"版本历史"]})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"版本 1.0"}),(0,r.jsx)("div",{className:"text-gray-500",children:"2024-07-04 10:30"})]}),(0,r.jsx)(n.z,{variant:"ghost",size:"sm",children:"查看"})]})}),(0,r.jsx)(n.z,{variant:"outline",size:"sm",className:"w-full mt-3",children:"查看所有版本"})]})]})]})]})})]})}},5974:function(e,s,t){"use strict";t.d(s,{C:function(){return i}});var r=t(7437);t(2265);var a=t(535),n=t(9554);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:t}),s),...a})}},2869:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var r=t(7437),a=t(2265),n=t(7053),l=t(535),i=t(9554);let c=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:l,asChild:d=!1,...o}=e,m=d?n.g7:"button";return(0,r.jsx)(m,{className:(0,i.cn)(c({variant:a,size:l,className:t})),ref:s,...o})});d.displayName="Button"},6070:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return l},aY:function(){return o},ll:function(){return c}});var r=t(7437),a=t(2265),n=t(9554);let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});l.displayName="Card";let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},1354:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var r=t(7437),a=t(2265),n=t(1994),l=t(3335);let i=a.forwardRef((e,s)=>{let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,className:function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,n.W)(s))}("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},9554:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var r=t(1994),a=t(3335);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.m6)((0,r.W)(s))}}},function(e){e.O(0,[635,171,971,117,744],function(){return e(e.s=1328)}),_N_E=e.O()}]);