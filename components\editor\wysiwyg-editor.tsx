'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  List,
  ListOrdered,
  Link,
  Image,
  Save,
  Undo,
  Redo,
  Type,
  Palette,
  FileText,
  Download,
  Upload
} from 'lucide-react'

interface WysiwygEditorProps {
  initialContent?: string
  onSave?: (content: string) => void
  onContentChange?: (content: string) => void
  placeholder?: string
  readOnly?: boolean
}

export default function WysiwygEditor({
  initialContent = '',
  onSave,
  onContentChange,
  placeholder = '开始编写您的投标文档...',
  readOnly = false
}: WysiwygEditorProps) {
  const [content, setContent] = useState(initialContent)
  const [isEditing, setIsEditing] = useState(false)
  const editorRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (editorRef.current && initialContent) {
      editorRef.current.innerHTML = initialContent
    }
  }, [initialContent])

  const executeCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value)
    handleContentChange()
  }

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML
      setContent(newContent)
      onContentChange?.(newContent)
    }
  }

  const handleSave = () => {
    onSave?.(content)
    setIsEditing(false)
  }

  const insertImage = () => {
    const url = prompt('请输入图片URL:')
    if (url) {
      executeCommand('insertImage', url)
    }
  }

  const insertLink = () => {
    const url = prompt('请输入链接URL:')
    if (url) {
      executeCommand('createLink', url)
    }
  }

  const changeFontSize = (size: string) => {
    executeCommand('fontSize', size)
  }

  const changeTextColor = (color: string) => {
    executeCommand('foreColor', color)
  }

  const toolbarButtons = [
    {
      group: '格式',
      buttons: [
        { icon: Bold, command: 'bold', title: '粗体' },
        { icon: Italic, command: 'italic', title: '斜体' },
        { icon: Underline, command: 'underline', title: '下划线' },
      ]
    },
    {
      group: '对齐',
      buttons: [
        { icon: AlignLeft, command: 'justifyLeft', title: '左对齐' },
        { icon: AlignCenter, command: 'justifyCenter', title: '居中对齐' },
        { icon: AlignRight, command: 'justifyRight', title: '右对齐' },
      ]
    },
    {
      group: '列表',
      buttons: [
        { icon: List, command: 'insertUnorderedList', title: '无序列表' },
        { icon: ListOrdered, command: 'insertOrderedList', title: '有序列表' },
      ]
    },
    {
      group: '插入',
      buttons: [
        { icon: Link, action: insertLink, title: '插入链接' },
        { icon: Image, action: insertImage, title: '插入图片' },
      ]
    },
    {
      group: '操作',
      buttons: [
        { icon: Undo, command: 'undo', title: '撤销' },
        { icon: Redo, command: 'redo', title: '重做' },
      ]
    }
  ]

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            文档编辑器
          </CardTitle>
          <div className="flex items-center space-x-2">
            {!readOnly && (
              <>
                <Button variant="outline" size="sm">
                  <Upload className="mr-1 h-3 w-3" />
                  导入
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-1 h-3 w-3" />
                  导出
                </Button>
                <Button onClick={handleSave} size="sm">
                  <Save className="mr-1 h-3 w-3" />
                  保存
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      {!readOnly && (
        <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-3">
          {/* 工具栏 */}
          <div className="flex flex-wrap items-center gap-4">
            {toolbarButtons.map((group, groupIndex) => (
              <div key={groupIndex} className="flex items-center space-x-1">
                {group.buttons.map((button, buttonIndex) => {
                  const Icon = button.icon
                  return (
                    <Button
                      key={buttonIndex}
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        if ('command' in button) {
                          executeCommand(button.command)
                        } else if ('action' in button) {
                          button.action()
                        }
                      }}
                      title={button.title}
                      className="h-8 w-8 p-0"
                    >
                      <Icon className="h-4 w-4" />
                    </Button>
                  )
                })}
                {groupIndex < toolbarButtons.length - 1 && (
                  <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />
                )}
              </div>
            ))}

            {/* 字体大小选择器 */}
            <select
              onChange={(e) => changeFontSize(e.target.value)}
              className="px-2 py-1 border border-gray-300 rounded text-sm bg-white dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="1">小</option>
              <option value="3" selected>正常</option>
              <option value="5">大</option>
              <option value="7">特大</option>
            </select>

            {/* 文字颜色选择器 */}
            <div className="flex items-center space-x-1">
              <Palette className="h-4 w-4 text-gray-500" />
              <input
                type="color"
                onChange={(e) => changeTextColor(e.target.value)}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="文字颜色"
              />
            </div>
          </div>
        </div>
      )}

      <CardContent className="p-0">
        <div
          ref={editorRef}
          contentEditable={!readOnly}
          onInput={handleContentChange}
          onFocus={() => setIsEditing(true)}
          className={`min-h-[400px] p-6 focus:outline-none ${
            readOnly ? 'cursor-default' : 'cursor-text'
          }`}
          style={{
            lineHeight: '1.6',
            fontSize: '16px'
          }}
          suppressContentEditableWarning={true}
        >
          {!content && !readOnly && (
            <div className="text-gray-400 pointer-events-none">
              {placeholder}
            </div>
          )}
        </div>
      </CardContent>

      {/* 状态栏 */}
      <div className="border-t border-gray-200 dark:border-gray-700 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <span>字数: {content.replace(/<[^>]*>/g, '').length}</span>
            <span>段落: {(content.match(/<p>/g) || []).length || 1}</span>
            {isEditing && (
              <span className="text-blue-600 dark:text-blue-400">
                正在编辑...
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <span>最后保存: 刚刚</span>
          </div>
        </div>
      </div>
    </Card>
  )
}
