import neo4j, { Driver, Session } from 'neo4j-driver'

if (!process.env.NEO4J_URI || !process.env.NEO4J_USERNAME || !process.env.NEO4J_PASSWORD) {
  throw new Error('Missing Neo4j environment variables')
}

const uri = process.env.NEO4J_URI
const username = process.env.NEO4J_USERNAME
const password = process.env.NEO4J_PASSWORD

let driver: Driver

if (process.env.NODE_ENV === 'development') {
  let globalWithNeo4j = global as typeof globalThis & {
    _neo4jDriver?: Driver
  }

  if (!globalWithNeo4j._neo4jDriver) {
    globalWithNeo4j._neo4jDriver = neo4j.driver(uri, neo4j.auth.basic(username, password))
  }
  driver = globalWithNeo4j._neo4jDriver
} else {
  driver = neo4j.driver(uri, neo4j.auth.basic(username, password))
}

export default driver

export function getSession(): Session {
  return driver.session()
}

export async function closeDriver(): Promise<void> {
  await driver.close()
}

// Neo4j 查询辅助函数
export async function runQuery(query: string, parameters: any = {}) {
  const session = getSession()
  try {
    const result = await session.run(query, parameters)
    return result.records
  } finally {
    await session.close()
  }
}

// 创建索引和约束
export async function initializeDatabase() {
  const session = getSession()
  try {
    // 创建项目节点约束
    await session.run(`
      CREATE CONSTRAINT project_id IF NOT EXISTS
      FOR (p:Project) REQUIRE p.id IS UNIQUE
    `)
    
    // 创建文档节点约束
    await session.run(`
      CREATE CONSTRAINT document_id IF NOT EXISTS
      FOR (d:Document) REQUIRE d.id IS UNIQUE
    `)
    
    // 创建章节节点约束
    await session.run(`
      CREATE CONSTRAINT section_id IF NOT EXISTS
      FOR (s:Section) REQUIRE s.id IS UNIQUE
    `)
    
    // 创建关键词节点约束
    await session.run(`
      CREATE CONSTRAINT keyword_name IF NOT EXISTS
      FOR (k:Keyword) REQUIRE k.name IS UNIQUE
    `)
    
    console.log('Neo4j database initialized successfully')
  } catch (error) {
    console.error('Error initializing Neo4j database:', error)
  } finally {
    await session.close()
  }
}
