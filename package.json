{"name": "tender-editor", "version": "1.0.0", "description": "投标文件编辑器 - 所见即所得的智能投标文件生成系统", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.300.0", "mongodb": "^6.0.0", "neo4j-driver": "^5.0.0", "next": "^14.0.0", "next-themes": "^0.4.6", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-dropzone": "^14.3.8", "react-markdown": "^9.0.0", "recharts": "^2.8.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "keywords": ["投标", "文件编辑器", "AI", "Next.js"], "author": "", "license": "MIT"}