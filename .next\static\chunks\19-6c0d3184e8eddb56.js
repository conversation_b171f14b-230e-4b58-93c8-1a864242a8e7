(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[19],{4766:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},4972:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},6475:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},8124:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])},5466:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},7692:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},8293:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},7226:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4630:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},8728:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5929:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2369:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2489:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9376:function(e,t,n){"use strict";var r=n(5475);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},911:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},6249:function(e,t,n){"use strict";n.d(t,{aU:function(){return ed},x8:function(){return ef},dk:function(){return ec},zt:function(){return ea},fC:function(){return el},Dx:function(){return eu},l_:function(){return es}});var r,o=n(2265),i=n.t(o,2),a=n(4887);function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var l=n(8575),u=n(7437);function c(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),a=n.length;n=[...n,r];let s=t=>{let{scope:n,children:r,...s}=t,l=n?.[e]?.[a]||i,c=o.useMemo(()=>s,Object.values(s));return(0,u.jsx)(l.Provider,{value:c,children:r})};return s.displayName=t+"Provider",[s,function(n,s){let l=s?.[e]?.[a]||i,u=o.useContext(l);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var d=n(7053),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,d.Z8)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(a,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function m(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}function p(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var v="dismissableLayer.update",y=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:h,onDismiss:w,...x}=e,b=o.useContext(y),[T,k]=o.useState(null),C=null!==(i=null==T?void 0:T.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,P]=o.useState({}),N=(0,l.e)(t,e=>k(e)),R=Array.from(b.layers),[L]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),S=R.indexOf(L),M=T?R.indexOf(T):-1,D=b.layersWithOutsidePointerEventsDisabled.size>0,j=M>=S,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=p(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){g("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!j||n||(null==d||d(e),null==h||h(e),e.defaultPrevented||null==w||w())},C),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=p(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&g("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(null==m||m(e),null==h||h(e),e.defaultPrevented||null==w||w())},C);return!function(e,t=globalThis?.document){let n=p(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M!==b.layers.size-1||(null==c||c(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},C),o.useEffect(()=>{if(T)return a&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(T)),b.layers.add(T),E(),()=>{a&&1===b.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[T,C,a,b]),o.useEffect(()=>()=>{T&&(b.layers.delete(T),b.layersWithOutsidePointerEventsDisabled.delete(T),E())},[T,b]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,u.jsx)(f.div,{...x,ref:N,style:{pointerEvents:D?j?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,O.onFocusCapture),onBlurCapture:s(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,A.onPointerDownCapture)})});h.displayName="DismissableLayer";var w=o.forwardRef((e,t)=>{let n=o.useContext(y),r=o.useRef(null),i=(0,l.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(f.div,{...e,ref:i})});function E(){let e=new CustomEvent(v);document.dispatchEvent(e)}function g(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?m(i,a):i.dispatchEvent(a)}w.displayName="DismissableLayerBranch";var x=globalThis?.document?o.useLayoutEffect:()=>{},b=o.forwardRef((e,t)=>{var n,r;let{container:i,...s}=e,[l,c]=o.useState(!1);x(()=>c(!0),[]);let d=i||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return d?a.createPortal((0,u.jsx)(f.div,{...s,ref:t}),d):null});b.displayName="Portal";var T=e=>{var t,n;let r,i;let{present:a,children:s}=e,u=function(e){var t,n;let[r,i]=o.useState(),a=o.useRef(null),s=o.useRef(e),l=o.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=k(a.current);l.current="mounted"===u?e:"none"},[u]),x(()=>{let t=a.current,n=s.current;if(n!==e){let r=l.current,o=k(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),x(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=k(a.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!s.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=k(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(a),c="function"==typeof s?s({present:u.isPresent}):o.Children.only(s),d=(0,l.e)(u.ref,(r=null===(t=Object.getOwnPropertyDescriptor(c.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning?c.ref:(r=null===(n=Object.getOwnPropertyDescriptor(c,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof s||u.isPresent?o.cloneElement(c,{ref:d}):null};function k(e){return(null==e?void 0:e.animationName)||"none"}T.displayName="Presence";var C=i[" useInsertionEffect ".trim().toString()]||x;Symbol("RADIX:SYNC_STATE");var P=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),N=o.forwardRef((e,t)=>(0,u.jsx)(f.span,{...e,ref:t,style:{...P,...e.style}}));N.displayName="VisuallyHidden";var R="ToastProvider",[L,S,M]=function(e){let t=e+"CollectionProvider",[n,r]=c(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=o.useRef(null),a=o.useRef(new Map).current;return(0,u.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};s.displayName=t;let f=e+"CollectionSlot",m=(0,d.Z8)(f),p=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=a(f,n),i=(0,l.e)(t,o.collectionRef);return(0,u.jsx)(m,{ref:i,children:r})});p.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=(0,d.Z8)(v),w=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,s=o.useRef(null),c=(0,l.e)(t,s),d=a(v,n);return o.useEffect(()=>(d.itemMap.set(s,{ref:s,...i}),()=>void d.itemMap.delete(s))),(0,u.jsx)(h,{[y]:"",ref:c,children:r})});return w.displayName=v,[{Provider:s,Slot:p,ItemSlot:w},function(t){let n=a(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}("Toast"),[D,j]=c("Toast",[M]),[A,O]=D(R),I=e=>{let{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=e,[l,c]=o.useState(null),[d,f]=o.useState(0),m=o.useRef(!1),p=o.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(R,"`. Expected non-empty `string`.")),(0,u.jsx)(L.Provider,{scope:t,children:(0,u.jsx)(A,{scope:t,label:n,duration:r,swipeDirection:i,swipeThreshold:a,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:o.useCallback(()=>f(e=>e+1),[]),onToastRemove:o.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:p,children:s})})};I.displayName=R;var F="ToastViewport",_=["F8"],Z="toast.viewportPause",K="toast.viewportResume",U=o.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:r=_,label:i="Notifications ({hotkey})",...a}=e,s=O(F,n),c=S(n),d=o.useRef(null),m=o.useRef(null),p=o.useRef(null),v=o.useRef(null),y=(0,l.e)(t,v,s.onViewportChange),h=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=s.toastCount>0;o.useEffect(()=>{let e=e=>{var t;0!==r.length&&r.every(t=>e[t]||e.code===t)&&(null===(t=v.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r]),o.useEffect(()=>{let e=d.current,t=v.current;if(E&&e&&t){let n=()=>{if(!s.isClosePausedRef.current){let e=new CustomEvent(Z);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},r=()=>{if(s.isClosePausedRef.current){let e=new CustomEvent(K);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[E,s.isClosePausedRef]);let g=o.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return o.useEffect(()=>{let e=v.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,o,i;let n=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(r=m.current)||void 0===r||r.focus();return}let s=g({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex(e=>e===n);ei(s.slice(l+1))?t.preventDefault():a?null===(o=m.current)||void 0===o||o.focus():null===(i=p.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,g]),(0,u.jsxs)(w,{ref:d,role:"region","aria-label":i.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&(0,u.jsx)(z,{ref:m,onFocusFromOutsideViewport:()=>{ei(g({tabbingDirection:"forwards"}))}}),(0,u.jsx)(L.Slot,{scope:n,children:(0,u.jsx)(f.ol,{tabIndex:-1,...a,ref:y})}),E&&(0,u.jsx)(z,{ref:p,onFocusFromOutsideViewport:()=>{ei(g({tabbingDirection:"backwards"}))}})]})});U.displayName=F;var W="ToastFocusProxy",z=o.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=O(W,n);return(0,u.jsx)(N,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(n))||r()}})});z.displayName=W;var V="Toast",H=o.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:a,...l}=e,[c,d]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,s]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return C(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[u,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else a(t)},[l,e,a,s])]}({prop:r,defaultProp:null==i||i,onChange:a,caller:V});return(0,u.jsx)(T,{present:n||c,children:(0,u.jsx)(B,{open:c,...l,ref:t,onClose:()=>d(!1),onPause:p(e.onPause),onResume:p(e.onResume),onSwipeStart:s(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:s(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:s(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:s(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),d(!1)})})})});H.displayName=V;var[q,$]=D(V,{onClose(){}}),B=o.forwardRef((e,t)=>{let{__scopeToast:n,type:r="foreground",duration:i,open:c,onClose:d,onEscapeKeyDown:m,onPause:v,onResume:y,onSwipeStart:w,onSwipeMove:E,onSwipeCancel:g,onSwipeEnd:x,...b}=e,T=O(V,n),[k,C]=o.useState(null),P=(0,l.e)(t,e=>C(e)),N=o.useRef(null),R=o.useRef(null),S=i||T.duration,M=o.useRef(0),D=o.useRef(S),j=o.useRef(0),{onToastAdd:A,onToastRemove:I}=T,F=p(()=>{var e;(null==k?void 0:k.contains(document.activeElement))&&(null===(e=T.viewport)||void 0===e||e.focus()),d()}),_=o.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(j.current),M.current=new Date().getTime(),j.current=window.setTimeout(F,e))},[F]);o.useEffect(()=>{let e=T.viewport;if(e){let t=()=>{_(D.current),null==y||y()},n=()=>{let e=new Date().getTime()-M.current;D.current=D.current-e,window.clearTimeout(j.current),null==v||v()};return e.addEventListener(Z,n),e.addEventListener(K,t),()=>{e.removeEventListener(Z,n),e.removeEventListener(K,t)}}},[T.viewport,S,v,y,_]),o.useEffect(()=>{c&&!T.isClosePausedRef.current&&_(S)},[c,S,T.isClosePausedRef,_]),o.useEffect(()=>(A(),()=>I()),[A,I]);let U=o.useMemo(()=>k?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}}),n}(k):null,[k]);return T.viewport?(0,u.jsxs)(u.Fragment,{children:[U&&(0,u.jsx)(X,{__scopeToast:n,role:"status","aria-live":"foreground"===r?"assertive":"polite","aria-atomic":!0,children:U}),(0,u.jsx)(q,{scope:n,onClose:F,children:a.createPortal((0,u.jsx)(L.ItemSlot,{scope:n,children:(0,u.jsx)(h,{asChild:!0,onEscapeKeyDown:s(m,()=>{T.isFocusedToastEscapeKeyDownRef.current||F(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,u.jsx)(f.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":T.swipeDirection,...b,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:s(e.onKeyDown,e=>{"Escape"!==e.key||(null==m||m(e.nativeEvent),e.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:s(e.onPointerDown,e=>{0===e.button&&(N.current={x:e.clientX,y:e.clientY})}),onPointerMove:s(e.onPointerMove,e=>{if(!N.current)return;let t=e.clientX-N.current.x,n=e.clientY-N.current.y,r=!!R.current,o=["left","right"].includes(T.swipeDirection),i=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,n),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};r?(R.current=u,er("toast.swipeMove",E,c,{discrete:!1})):eo(u,T.swipeDirection,l)?(R.current=u,er("toast.swipeStart",w,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(N.current=null)}),onPointerUp:s(e.onPointerUp,e=>{let t=R.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),R.current=null,N.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};eo(t,T.swipeDirection,T.swipeThreshold)?er("toast.swipeEnd",x,r,{discrete:!0}):er("toast.swipeCancel",g,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),X=e=>{let{__scopeToast:t,children:n,...r}=e,i=O(V,t),[a,s]=o.useState(!1),[l,c]=o.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=p(e);x(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>s(!0)),o.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,u.jsx)(b,{asChild:!0,children:(0,u.jsx)(N,{...r,children:a&&(0,u.jsxs)(u.Fragment,{children:[i.label," ",n]})})})},Y=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,u.jsx)(f.div,{...r,ref:t})});Y.displayName="ToastTitle";var J=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,u.jsx)(f.div,{...r,ref:t})});J.displayName="ToastDescription";var G="ToastAction",Q=o.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,u.jsx)(en,{altText:n,asChild:!0,children:(0,u.jsx)(et,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(G,"`. Expected non-empty `string`.")),null)});Q.displayName=G;var ee="ToastClose",et=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=$(ee,n);return(0,u.jsx)(en,{asChild:!0,children:(0,u.jsx)(f.button,{type:"button",...r,ref:t,onClick:s(e.onClick,o.onClose)})})});et.displayName=ee;var en=o.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,u.jsx)(f.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function er(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?m(i,a):i.dispatchEvent(a)}var eo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function ei(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ea=I,es=U,el=H,eu=Y,ec=J,ed=Q,ef=et},5922:function(e,t,n){"use strict";n.d(t,{F:function(){return c},f:function(){return d}});var r=n(2265),o=(e,t,n,r,o,i,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?o.map(e=>i[e]||e):o;n?(l.classList.remove(...r),l.classList.add(i&&i[t]?i[t]:t)):l.setAttribute(e,t)}),s&&u.includes(t)&&(l.style.colorScheme=t)}if(r)c(r);else try{let e=localStorage.getItem(t)||n,r=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(r)}catch(e){}},i=["light","dark"],a="(prefers-color-scheme: dark)",s="undefined"==typeof window,l=r.createContext(void 0),u={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=r.useContext(l))?e:u},d=e=>r.useContext(l)?r.createElement(r.Fragment,null,e.children):r.createElement(m,{...e}),f=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:c=f,defaultTheme:d=o?"system":"light",attribute:m="data-theme",value:w,children:E,nonce:g,scriptProps:x}=e,[b,T]=r.useState(()=>v(u,d)),[k,C]=r.useState(()=>"system"===b?h():b),P=w?Object.values(w):c,N=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=h());let r=w?w[t]:t,a=n?y(g):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...P),r&&l.classList.add(r)):e.startsWith("data-")&&(r?l.setAttribute(e,r):l.removeAttribute(e))};if(Array.isArray(m)?m.forEach(u):u(m),s){let e=i.includes(d)?d:null,n=i.includes(t)?t:e;l.style.colorScheme=n}null==a||a()},[g]),R=r.useCallback(e=>{let t="function"==typeof e?e(b):e;T(t);try{localStorage.setItem(u,t)}catch(e){}},[b]),L=r.useCallback(e=>{C(h(e)),"system"===b&&o&&!t&&N("system")},[b,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),r.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?T(e.newValue):R(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[R]),r.useEffect(()=>{N(null!=t?t:b)},[t,b]);let S=r.useMemo(()=>({theme:b,setTheme:R,forcedTheme:t,resolvedTheme:"system"===b?k:b,themes:o?[...c,"system"]:c,systemTheme:o?k:void 0}),[b,R,t,k,o,c]);return r.createElement(l.Provider,{value:S},r.createElement(p,{forcedTheme:t,storageKey:u,attribute:m,enableSystem:o,enableColorScheme:s,defaultTheme:d,value:w,themes:c,nonce:g,scriptProps:x}),E)},p=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:i,enableSystem:a,enableColorScheme:s,defaultTheme:l,value:u,themes:c,nonce:d,scriptProps:f}=e,m=JSON.stringify([i,n,l,t,c,u,a,s]).slice(1,-1);return r.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?d:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(m,")")}})}),v=(e,t)=>{let n;if(!s){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},h=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")}}]);