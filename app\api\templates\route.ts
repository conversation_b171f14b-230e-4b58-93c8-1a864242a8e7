import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    
    const category = searchParams.get('category')
    const type = searchParams.get('type')
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // 构建查询条件
    const query: any = {}
    if (category && category !== 'all') {
      query.category = category
    }
    if (type && type !== 'all') {
      query.type = type
    }
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ]
    }

    const templates = await db.collection('templates')
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray()

    const total = await db.collection('templates').countDocuments(query)

    return NextResponse.json({
      templates: templates.map(template => ({
        ...template,
        id: template._id.toString(),
        _id: undefined
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('获取模板失败:', error)
    return NextResponse.json(
      { error: '获取模板失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const body = await request.json()

    const template = {
      name: body.name,
      description: body.description,
      category: body.category,
      type: body.type,
      content: body.content,
      tags: body.tags || [],
      isPublic: body.isPublic || false,
      isFavorite: false,
      usageCount: 0,
      rating: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.createdBy || '系统用户'
    }

    const result = await db.collection('templates').insertOne(template)

    return NextResponse.json({
      id: result.insertedId.toString(),
      ...template
    }, { status: 201 })
  } catch (error) {
    console.error('创建模板失败:', error)
    return NextResponse.json(
      { error: '创建模板失败' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: '模板ID不能为空' },
        { status: 400 }
      )
    }

    const updateFields = {
      ...updateData,
      updatedAt: new Date().toISOString()
    }

    const result = await db.collection('templates').updateOne(
      { _id: id },
      { $set: updateFields }
    )

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('更新模板失败:', error)
    return NextResponse.json(
      { error: '更新模板失败' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: '模板ID不能为空' },
        { status: 400 }
      )
    }

    const result = await db.collection('templates').deleteOne({ _id: new ObjectId(id) })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('删除模板失败:', error)
    return NextResponse.json(
      { error: '删除模板失败' },
      { status: 500 }
    )
  }
}
