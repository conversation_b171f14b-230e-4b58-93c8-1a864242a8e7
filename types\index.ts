import { ObjectId } from 'mongodb'

// 基础类型
export interface BaseEntity {
  _id?: ObjectId
  id?: string
  createdAt: Date
  updatedAt: Date
}

// 用户类型
export interface User extends BaseEntity {
  name: string
  email: string
  role: 'admin' | 'manager' | 'editor' | 'viewer'
  avatar?: string
  preferences: {
    theme: 'light' | 'dark'
    language: 'zh-CN' | 'en-US'
  }
}

// 项目类型
export interface Project extends BaseEntity {
  name: string
  description: string
  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'archived'
  type: 'construction' | 'service' | 'goods' | 'other'
  tenderInfo: {
    tenderNumber: string
    tenderName: string
    tenderOrganization: string
    deadline: Date
    budget?: number
    requirements: string[]
  }
  participants: {
    userId: string
    role: 'owner' | 'editor' | 'reviewer'
    joinedAt: Date
  }[]
  documents: string[] // Document IDs
  templates: string[] // Template IDs
  aiAnalysis?: string[] // AI Analysis IDs
  tags: string[]
  progress: {
    totalSections: number
    completedSections: number
    percentage: number
  }
}

// 文档类型
export interface Document extends BaseEntity {
  projectId: string
  name: string
  type: 'tender_document' | 'reference' | 'template' | 'generated'
  content: string
  format: 'pdf' | 'docx' | 'txt' | 'markdown'
  size: number
  url?: string
  metadata: {
    author?: string
    version: string
    language: string
    keywords: string[]
  }
  sections: DocumentSection[]
  aiAnalysis?: {
    analysisId: string
    summary: string
    keyPoints: string[]
    requirements: string[]
    suggestions: string[]
  }
}

// 文档章节类型
export interface DocumentSection {
  id: string
  title: string
  content: string
  order: number
  type: 'text' | 'table' | 'image' | 'chart'
  metadata: {
    wordCount: number
    keywords: string[]
    importance: 'high' | 'medium' | 'low'
  }
  subsections?: DocumentSection[]
}

// 模板类型
export interface Template extends BaseEntity {
  name: string
  description: string
  category: 'construction' | 'service' | 'goods' | 'general'
  type: 'full_template' | 'section_template' | 'clause_template'
  content: TemplateSection[]
  variables: TemplateVariable[]
  usage: {
    count: number
    lastUsed: Date
  }
  isPublic: boolean
  tags: string[]
}

// 模板章节类型
export interface TemplateSection {
  id: string
  title: string
  content: string
  order: number
  required: boolean
  variables: string[] // Variable names used in this section
  subsections?: TemplateSection[]
  metadata: {
    estimatedWords: number
    complexity: 'simple' | 'medium' | 'complex'
    category: string
  }
}

// 模板变量类型
export interface TemplateVariable {
  name: string
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'boolean'
  label: string
  description?: string
  required: boolean
  defaultValue?: any
  options?: string[] // For select/multiselect types
  validation?: {
    min?: number
    max?: number
    pattern?: string
  }
}

// AI分析类型
export interface AIAnalysis extends BaseEntity {
  documentId: string
  projectId: string
  type: 'document_analysis' | 'requirement_extraction' | 'content_generation'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  input: {
    prompt: string
    parameters: Record<string, any>
  }
  output: {
    summary?: string
    keyPoints?: string[]
    requirements?: string[]
    suggestions?: string[]
    generatedContent?: string
    confidence: number
  }
  metadata: {
    aiProvider: 'deepseek' | 'apiyi'
    model: string
    processingTime: number
    tokenUsage: {
      input: number
      output: number
    }
  }
}

// 文件类型
export interface FileUpload extends BaseEntity {
  originalName: string
  filename: string
  mimetype: string
  size: number
  path: string
  url: string
  projectId?: string
  documentId?: string
  uploadedBy: string
  metadata: {
    width?: number
    height?: number
    duration?: number
    pages?: number
  }
}

// 搜索结果类型
export interface SearchResult {
  id: string
  type: 'project' | 'document' | 'template' | 'section'
  title: string
  content: string
  score: number
  highlights: string[]
  metadata: Record<string, any>
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 分页参数类型
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}
