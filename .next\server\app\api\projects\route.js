"use strict";(()=>{var e={};e.id=871,e.ids=[871],e.modules={8013:e=>{e.exports=require("mongodb")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1838:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>j,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>p});var o=r(9303),n=r(8716),a=r(670),i=r(7070),c=r(2021);async function u(e){try{let{db:t}=await (0,c.vO)(),{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),o=parseInt(r.get("limit")||"10"),n=r.get("status"),a=r.get("search"),u={};n&&"all"!==n&&(u.status=n),a&&(u.$or=[{name:{$regex:a,$options:"i"}},{tenderNumber:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}}]);let p=(s-1)*o,d=await t.collection("projects").find(u).sort({updatedAt:-1}).skip(p).limit(o).toArray(),l=await t.collection("projects").countDocuments(u);return i.NextResponse.json({success:!0,data:{projects:d,pagination:{page:s,limit:o,total:l,pages:Math.ceil(l/o)}}})}catch(e){return console.error("获取项目列表失败:",e),i.NextResponse.json({success:!1,error:"获取项目列表失败"},{status:500})}}async function p(e){try{let{db:t}=await (0,c.vO)(),r=await e.json(),{name:s,description:o,deadline:n,budget:a,tenderNumber:u}=r;if(!s||!o||!n||!u)return i.NextResponse.json({success:!1,error:"缺少必填字段"},{status:400});if(await t.collection("projects").findOne({tenderNumber:u}))return i.NextResponse.json({success:!1,error:"招标编号已存在"},{status:400});let p={...r,status:r.status||"pending",progress:0,documents:[],team:r.team||[],createdAt:new Date,updatedAt:new Date,createdBy:"current-user"},d=await t.collection("projects").insertOne(p);return i.NextResponse.json({success:!0,data:{id:d.insertedId,...p}},{status:201})}catch(e){return console.error("创建项目失败:",e),i.NextResponse.json({success:!1,error:"创建项目失败"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"E:\\rjkf\\tb-0704-V\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:j}=d,m="/api/projects/route";function x(){return(0,a.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:g})}},2021:(e,t,r)=>{let s;async function o(){let e=await s,t=e.db("tender-editor");return{client:e,db:t}}r.d(t,{vO:()=>o}),s=new(r(8013)).MongoClient("mongodb://localhost:27017/tender-editor",{}).connect()}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,972],()=>r(1838));module.exports=s})();