'use client'

import { useState, useEffect, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Upload, 
  Search, 
  Filter, 
  MoreHorizontal,
  FileText,
  Image,
  File,
  Download,
  Eye,
  Edit,
  Trash2,
  FolderOpen,
  Calendar,
  User,
  HardDrive,
  Cloud,
  Plus,
  Grid,
  List,
  SortAsc
} from 'lucide-react'

interface FileItem {
  id: string
  name: string
  type: 'document' | 'image' | 'other'
  size: string
  uploadedAt: string
  uploadedBy: string
  project?: string
  tags: string[]
  version: number
  status: 'processing' | 'ready' | 'error'
}

export default function FilesPage() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [loading, setLoading] = useState(true)
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({})

  useEffect(() => {
    // 模拟加载文件数据
    setTimeout(() => {
      setFiles([
        {
          id: '1',
          name: '技术规范说明书.docx',
          type: 'document',
          size: '2.5 MB',
          uploadedAt: '2024-07-02 14:30',
          uploadedBy: '张工程师',
          project: '某市政道路建设项目',
          tags: ['技术规范', '施工标准'],
          version: 3,
          status: 'ready'
        },
        {
          id: '2',
          name: '施工现场图片.jpg',
          type: 'image',
          size: '1.8 MB',
          uploadedAt: '2024-07-01 16:45',
          uploadedBy: '李项目经理',
          project: '办公楼装修工程',
          tags: ['现场照片', '进度记录'],
          version: 1,
          status: 'ready'
        },
        {
          id: '3',
          name: '投标文件模板.pdf',
          type: 'document',
          size: '5.2 MB',
          uploadedAt: '2024-06-30 09:15',
          uploadedBy: '王技术员',
          project: 'IT设备采购项目',
          tags: ['模板', '标准格式'],
          version: 2,
          status: 'ready'
        },
        {
          id: '4',
          name: '设备清单.xlsx',
          type: 'document',
          size: '890 KB',
          uploadedAt: '2024-06-29 11:20',
          uploadedBy: '陈设计师',
          project: '学校设备采购',
          tags: ['设备清单', '采购'],
          version: 1,
          status: 'processing'
        },
        {
          id: '5',
          name: '合同附件.zip',
          type: 'other',
          size: '12.3 MB',
          uploadedAt: '2024-06-28 13:55',
          uploadedBy: '刘监理',
          project: '医院设备更新',
          tags: ['合同', '附件'],
          version: 1,
          status: 'ready'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach((file) => {
      const fileId = Date.now().toString() + Math.random().toString(36).substr(2, 9)
      
      // 模拟上传进度
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }))
      
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileId] || 0
          if (currentProgress >= 100) {
            clearInterval(interval)
            // 添加到文件列表
            const newFile: FileItem = {
              id: fileId,
              name: file.name,
              type: file.type.startsWith('image/') ? 'image' : 
                    file.type.includes('document') || file.name.endsWith('.pdf') || 
                    file.name.endsWith('.docx') || file.name.endsWith('.xlsx') ? 'document' : 'other',
              size: formatFileSize(file.size),
              uploadedAt: new Date().toLocaleString('zh-CN'),
              uploadedBy: '当前用户',
              tags: [],
              version: 1,
              status: 'ready'
            }
            setFiles(prev => [newFile, ...prev])
            return { ...prev, [fileId]: 100 }
          }
          return { ...prev, [fileId]: currentProgress + 10 }
        })
      }, 200)
    })
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
      'application/zip': ['.zip'],
      'text/*': ['.txt']
    }
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'document':
        return FileText
      case 'image':
        return Image
      default:
        return File
    }
  }

  const getStatusInfo = (status: string) => {
    const statusMap = {
      processing: { color: 'bg-yellow-100 text-yellow-800', label: '处理中' },
      ready: { color: 'bg-green-100 text-green-800', label: '就绪' },
      error: { color: 'bg-red-100 text-red-800', label: '错误' }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.ready
  }

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesFilter = filterType === 'all' || file.type === filterType
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">加载文件数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">文件管理</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                管理您的项目文件，支持多种格式上传和版本控制
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建文件夹
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 文件上传区域 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="mr-2 h-5 w-5" />
              文件上传
            </CardTitle>
            <CardDescription>
              支持拖拽上传，或点击选择文件。支持 PDF、Word、Excel、图片等格式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Cloud className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              {isDragActive ? (
                <p className="text-blue-600 font-medium">释放文件以开始上传...</p>
              ) : (
                <div>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    拖拽文件到此处，或 <span className="text-blue-600 font-medium">点击选择文件</span>
                  </p>
                  <p className="text-sm text-gray-500">
                    支持 PDF、Word、Excel、图片、压缩包等格式，单个文件最大 50MB
                  </p>
                </div>
              )}
            </div>

            {/* 上传进度 */}
            {Object.keys(uploadProgress).length > 0 && (
              <div className="mt-4 space-y-2">
                {Object.entries(uploadProgress).map(([fileId, progress]) => (
                  <div key={fileId} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>上传中...</span>
                        <span>{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 搜索和筛选 */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索文件名或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="all">全部类型</option>
              <option value="document">文档</option>
              <option value="image">图片</option>
              <option value="other">其他</option>
            </select>
            <Button variant="outline">
              <SortAsc className="mr-2 h-4 w-4" />
              排序
            </Button>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>
        </div>

        {/* 文件统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">文档</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {files.filter(f => f.type === 'document').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Image className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">图片</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {files.filter(f => f.type === 'image').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <File className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">其他</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {files.filter(f => f.type === 'other').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <HardDrive className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">总大小</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    24.7 MB
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 文件列表 */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredFiles.map((file) => {
              const FileIcon = getFileIcon(file.type)
              const statusInfo = getStatusInfo(file.status)
              
              return (
                <Card key={file.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <FileIcon className="h-6 w-6 text-gray-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            v{file.version} • {file.size}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                    <Badge className={statusInfo.color}>
                      {statusInfo.label}
                    </Badge>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="text-xs text-gray-500">
                        <div className="flex items-center mb-1">
                          <User className="mr-1 h-3 w-3" />
                          {file.uploadedBy}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          {file.uploadedAt}
                        </div>
                      </div>
                      
                      {file.project && (
                        <div className="text-xs">
                          <span className="text-gray-500">项目：</span>
                          <span className="text-blue-600">{file.project}</span>
                        </div>
                      )}

                      {file.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {file.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      <div className="flex space-x-1 pt-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="mr-1 h-3 w-3" />
                          查看
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        文件名
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        大小
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        上传者
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        上传时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredFiles.map((file) => {
                      const FileIcon = getFileIcon(file.type)
                      const statusInfo = getStatusInfo(file.status)
                      
                      return (
                        <tr key={file.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <FileIcon className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {file.name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  版本 {file.version}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {file.size}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {file.uploadedBy}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {file.uploadedAt}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={statusInfo.color}>
                              {statusInfo.label}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {filteredFiles.length === 0 && (
          <div className="text-center py-12">
            <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              没有找到文件
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              尝试调整搜索条件或上传新文件
            </p>
          </div>
        )}
      </main>
    </div>
  )
}
