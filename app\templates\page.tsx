'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  FileText,
  Search,
  Filter,
  Plus,
  Download,
  Eye,
  Edit,
  Copy,
  Star,
  Clock,
  User,
  Tag,
  Grid,
  List,
  MoreHorizontal,
  BookOpen,
  Layers,
  Target,
  Zap
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  category: 'technical' | 'commercial' | 'legal' | 'management'
  type: 'section' | 'document' | 'clause'
  content: string
  tags: string[]
  isPublic: boolean
  isFavorite: boolean
  usageCount: number
  rating: number
  createdAt: string
  updatedAt: string
  createdBy: string
  size: string
}

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟加载模板数据
    setTimeout(() => {
      setTemplates([
        {
          id: '1',
          name: '技术方案标准模板',
          description: '适用于建筑工程项目的技术方案编写，包含完整的技术要点和实施计划',
          category: 'technical',
          type: 'document',
          content: '# 技术方案\n\n## 1. 项目概述\n...',
          tags: ['建筑工程', '技术方案', '施工'],
          isPublic: true,
          isFavorite: true,
          usageCount: 156,
          rating: 4.8,
          createdAt: '2024-06-15 10:30',
          updatedAt: '2024-07-01 14:20',
          createdBy: '系统管理员',
          size: '15.2 KB'
        },
        {
          id: '2',
          name: '商务标准条款',
          description: '包含常用的商务条款和合同条件，适用于各类工程项目投标',
          category: 'commercial',
          type: 'section',
          content: '## 商务条款\n\n### 付款方式\n...',
          tags: ['商务', '合同', '付款'],
          isPublic: true,
          isFavorite: false,
          usageCount: 89,
          rating: 4.5,
          createdAt: '2024-06-10 16:45',
          updatedAt: '2024-06-25 09:15',
          createdBy: '张商务经理',
          size: '8.7 KB'
        },
        {
          id: '3',
          name: '质量保证措施',
          description: '详细的质量管理体系和保证措施，确保工程质量达标',
          category: 'management',
          type: 'section',
          content: '## 质量保证措施\n\n### 质量管理体系\n...',
          tags: ['质量管理', '保证措施', 'ISO'],
          isPublic: true,
          isFavorite: true,
          usageCount: 234,
          rating: 4.9,
          createdAt: '2024-05-20 11:20',
          updatedAt: '2024-06-30 16:40',
          createdBy: '李质量总监',
          size: '12.5 KB'
        },
        {
          id: '4',
          name: '法律声明条款',
          description: '标准的法律声明和免责条款，保护企业合法权益',
          category: 'legal',
          type: 'clause',
          content: '### 法律声明\n\n本投标文件中的所有内容...',
          tags: ['法律', '声明', '免责'],
          isPublic: false,
          isFavorite: false,
          usageCount: 45,
          rating: 4.2,
          createdAt: '2024-06-05 14:10',
          updatedAt: '2024-06-20 10:30',
          createdBy: '王法务顾问',
          size: '3.8 KB'
        },
        {
          id: '5',
          name: '项目管理计划',
          description: '完整的项目管理计划模板，包含进度、资源、风险管理',
          category: 'management',
          type: 'document',
          content: '# 项目管理计划\n\n## 1. 项目组织架构\n...',
          tags: ['项目管理', '进度计划', '风险控制'],
          isPublic: true,
          isFavorite: false,
          usageCount: 178,
          rating: 4.7,
          createdAt: '2024-05-15 09:45',
          updatedAt: '2024-06-28 15:20',
          createdBy: '陈项目经理',
          size: '22.1 KB'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getCategoryInfo = (category: string) => {
    const categoryMap = {
      technical: { label: '技术方案', color: 'bg-blue-100 text-blue-800', icon: Zap },
      commercial: { label: '商务条款', color: 'bg-green-100 text-green-800', icon: Target },
      legal: { label: '法律条款', color: 'bg-purple-100 text-purple-800', icon: BookOpen },
      management: { label: '项目管理', color: 'bg-orange-100 text-orange-800', icon: Layers }
    }
    return categoryMap[category as keyof typeof categoryMap] || categoryMap.technical
  }

  const getTypeInfo = (type: string) => {
    const typeMap = {
      document: { label: '完整文档', icon: FileText },
      section: { label: '文档章节', icon: Layers },
      clause: { label: '条款片段', icon: Tag }
    }
    return typeMap[type as keyof typeof typeMap] || typeMap.document
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = filterCategory === 'all' || template.category === filterCategory
    const matchesType = filterType === 'all' || template.type === filterType
    return matchesSearch && matchesCategory && matchesType
  })

  const toggleFavorite = (templateId: string) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { ...template, isFavorite: !template.isFavorite }
        : template
    ))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">加载模板数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <FileText className="mr-3 h-8 w-8 text-blue-600" />
                模板管理
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                管理和使用投标文档模板，提高文档编写效率
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新建模板
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和筛选 */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索模板名称、描述或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="all">全部分类</option>
              <option value="technical">技术方案</option>
              <option value="commercial">商务条款</option>
              <option value="legal">法律条款</option>
              <option value="management">项目管理</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="all">全部类型</option>
              <option value="document">完整文档</option>
              <option value="section">文档章节</option>
              <option value="clause">条款片段</option>
            </select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>
        </div>

        {/* 模板统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">总模板数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{templates.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Star className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">收藏模板</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {templates.filter(t => t.isFavorite).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Eye className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">总使用次数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {templates.reduce((sum, t) => sum + t.usageCount, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <User className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">公开模板</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {templates.filter(t => t.isPublic).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 模板列表 */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => {
              const categoryInfo = getCategoryInfo(template.category)
              const typeInfo = getTypeInfo(template.type)
              const CategoryIcon = categoryInfo.icon
              const TypeIcon = typeInfo.icon

              return (
                <Card key={template.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <TypeIcon className="h-6 w-6 text-gray-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                            {template.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {template.size} • 使用 {template.usageCount} 次
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => toggleFavorite(template.id)}
                        >
                          <Star 
                            className={`h-4 w-4 ${
                              template.isFavorite 
                                ? 'text-yellow-500 fill-current' 
                                : 'text-gray-400'
                            }`} 
                          />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                        {template.description}
                      </p>

                      <div className="flex items-center space-x-2">
                        <Badge className={categoryInfo.color}>
                          <CategoryIcon className="mr-1 h-3 w-3" />
                          {categoryInfo.label}
                        </Badge>
                        <Badge variant="secondary">
                          {typeInfo.label}
                        </Badge>
                        {!template.isPublic && (
                          <Badge variant="outline">私有</Badge>
                        )}
                      </div>

                      {template.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {template.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {template.tags.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center">
                          <User className="mr-1 h-3 w-3" />
                          {template.createdBy}
                        </div>
                        <div className="flex items-center">
                          <Clock className="mr-1 h-3 w-3" />
                          {template.updatedAt}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < Math.floor(template.rating)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                          <span className="text-xs text-gray-500 ml-1">
                            {template.rating}
                          </span>
                        </div>
                      </div>

                      <div className="flex space-x-1 pt-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="mr-1 h-3 w-3" />
                          预览
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Copy className="mr-1 h-3 w-3" />
                          使用
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        模板名称
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分类
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        类型
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        使用次数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        评分
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        更新时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredTemplates.map((template) => {
                      const categoryInfo = getCategoryInfo(template.category)
                      const typeInfo = getTypeInfo(template.type)
                      const TypeIcon = typeInfo.icon

                      return (
                        <tr key={template.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <TypeIcon className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {template.name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {template.size}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={categoryInfo.color}>
                              {categoryInfo.label}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant="secondary">
                              {typeInfo.label}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {template.usageCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex items-center space-x-1">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < Math.floor(template.rating)
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-gray-500 ml-2">
                                {template.rating}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {template.updatedAt}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <FileTemplate className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              没有找到模板
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              尝试调整搜索条件或创建新模板
            </p>
            <div className="mt-6">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建新模板
              </Button>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
