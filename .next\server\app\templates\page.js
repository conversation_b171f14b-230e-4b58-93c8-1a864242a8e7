(()=>{var e={};e.id=0,e.ids=[0],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5990:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d}),t(2099),t(6070),t(5866);var a=t(3191),r=t(8716),l=t(7922),i=t.n(l),c=t(5231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2099)),"E:\\rjkf\\tb-0704-V\\app\\templates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6070)),"E:\\rjkf\\tb-0704-V\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],x=["E:\\rjkf\\tb-0704-V\\app\\templates\\page.tsx"],o="/templates/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/templates/page",pathname:"/templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8107:(e,s,t)=>{Promise.resolve().then(t.bind(t,6999))},6999:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(326),r=t(7577),l=t(772),i=t(2643),c=t(567),n=t(8715),d=t(3634),x=t(3468),o=t(6557);let m=(0,o.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);var h=t(9163),p=t(6283),g=t(765),u=t(9389),y=t(924),j=t(3855),f=t(8307),v=t(1137),N=t(3734),b=t(2714),w=t(9635),k=t(9216),Z=t(8998);let z=(0,o.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var C=t(1540),M=t(3);function P(){let[e,s]=(0,r.useState)([]),[t,o]=(0,r.useState)(""),[P,_]=(0,r.useState)("all"),[q,E]=(0,r.useState)("all"),[S,L]=(0,r.useState)("grid"),[R,F]=(0,r.useState)(!0),Y=e=>{let s={technical:{label:"技术方案",color:"bg-blue-100 text-blue-800",icon:d.Z},commercial:{label:"商务条款",color:"bg-green-100 text-green-800",icon:x.Z},legal:{label:"法律条款",color:"bg-purple-100 text-purple-800",icon:m},management:{label:"项目管理",color:"bg-orange-100 text-orange-800",icon:h.Z}};return s[e]||s.technical},A=e=>{let s={document:{label:"完整文档",icon:p.Z},section:{label:"文档章节",icon:h.Z},clause:{label:"条款片段",icon:g.Z}};return s[e]||s.document},V=e.filter(e=>{let s=e.name.toLowerCase().includes(t.toLowerCase())||e.description.toLowerCase().includes(t.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(t.toLowerCase())),a="all"===P||e.category===P,r="all"===q||e.type===q;return s&&a&&r}),G=e=>{s(s=>s.map(s=>s.id===e?{...s,isFavorite:!s.isFavorite}:s))};return R?a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"加载模板数据中..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white flex items-center",children:[a.jsx(p.Z,{className:"mr-3 h-8 w-8 text-blue-600"}),"模板管理"]}),a.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"管理和使用投标文档模板，提高文档编写效率"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(l.z,{variant:"outline",onClick:()=>L("grid"===S?"list":"grid"),children:"grid"===S?a.jsx(u.Z,{className:"h-4 w-4"}):a.jsx(y.Z,{className:"h-4 w-4"})}),(0,a.jsxs)(l.z,{children:[a.jsx(j.Z,{className:"mr-2 h-4 w-4"}),"新建模板"]})]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[a.jsx(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),a.jsx(n.I,{placeholder:"搜索模板名称、描述或标签...",value:t,onChange:e=>o(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:P,onChange:e=>_(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600",children:[a.jsx("option",{value:"all",children:"全部分类"}),a.jsx("option",{value:"technical",children:"技术方案"}),a.jsx("option",{value:"commercial",children:"商务条款"}),a.jsx("option",{value:"legal",children:"法律条款"}),a.jsx("option",{value:"management",children:"项目管理"})]}),(0,a.jsxs)("select",{value:q,onChange:e=>E(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600",children:[a.jsx("option",{value:"all",children:"全部类型"}),a.jsx("option",{value:"document",children:"完整文档"}),a.jsx("option",{value:"section",children:"文档章节"}),a.jsx("option",{value:"clause",children:"条款片段"})]}),(0,a.jsxs)(l.z,{variant:"outline",children:[a.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"筛选"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(p.Z,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"总模板数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.length})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(N.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"收藏模板"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>e.isFavorite).length})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(b.Z,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"总使用次数"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.reduce((e,s)=>e+s.usageCount,0)})]})]})})}),a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:a.jsx(w.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"公开模板"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>e.isPublic).length})]})]})})})]}),"grid"===S?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:V.map(e=>{let s=Y(e.category),t=A(e.type),r=s.icon,n=t.icon;return(0,a.jsxs)(i.Zb,{className:"hover:shadow-lg transition-shadow",children:[a.jsx(i.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:a.jsx(n,{className:"h-6 w-6 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white truncate",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.size," • 使用 ",e.usageCount," 次"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(l.z,{variant:"ghost",size:"icon",onClick:()=>G(e.id),children:a.jsx(N.Z,{className:`h-4 w-4 ${e.isFavorite?"text-yellow-500 fill-current":"text-gray-400"}`})}),a.jsx(l.z,{variant:"ghost",size:"icon",children:a.jsx(k.Z,{className:"h-4 w-4"})})]})]})}),a.jsx(i.aY,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.C,{className:s.color,children:[a.jsx(r,{className:"mr-1 h-3 w-3"}),s.label]}),a.jsx(c.C,{variant:"secondary",children:t.label}),!e.isPublic&&a.jsx(c.C,{variant:"outline",children:"私有"})]}),e.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,s)=>a.jsx(c.C,{variant:"secondary",className:"text-xs",children:e},s)),e.tags.length>3&&(0,a.jsxs)(c.C,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(w.Z,{className:"mr-1 h-3 w-3"}),e.createdBy]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(Z.Z,{className:"mr-1 h-3 w-3"}),e.updatedAt]})]}),a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[[void 0,void 0,void 0,void 0,void 0].map((s,t)=>a.jsx(N.Z,{className:`h-3 w-3 ${t<Math.floor(e.rating)?"text-yellow-400 fill-current":"text-gray-300"}`},t)),a.jsx("span",{className:"text-xs text-gray-500 ml-1",children:e.rating})]})}),(0,a.jsxs)("div",{className:"flex space-x-1 pt-2",children:[(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"flex-1",children:[a.jsx(b.Z,{className:"mr-1 h-3 w-3"}),"预览"]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",className:"flex-1",children:[a.jsx(z,{className:"mr-1 h-3 w-3"}),"使用"]}),a.jsx(l.z,{variant:"outline",size:"sm",children:a.jsx(C.Z,{className:"h-3 w-3"})}),a.jsx(l.z,{variant:"outline",size:"sm",children:a.jsx(M.Z,{className:"h-3 w-3"})})]})]})})]},e.id)})}):a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-0",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"模板名称"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"使用次数"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"评分"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新时间"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:V.map(e=>{let s=Y(e.category),t=A(e.type),r=t.icon;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(r,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.size})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx(c.C,{className:s.color,children:s.label})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx(c.C,{variant:"secondary",children:t.label})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.usageCount}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>a.jsx(N.Z,{className:`h-3 w-3 ${t<Math.floor(e.rating)?"text-yellow-400 fill-current":"text-gray-300"}`},t))}),a.jsx("span",{className:"text-sm text-gray-500 ml-2",children:e.rating})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(l.z,{variant:"ghost",size:"sm",children:a.jsx(b.Z,{className:"h-4 w-4"})}),a.jsx(l.z,{variant:"ghost",size:"sm",children:a.jsx(z,{className:"h-4 w-4"})}),a.jsx(l.z,{variant:"ghost",size:"sm",children:a.jsx(C.Z,{className:"h-4 w-4"})}),a.jsx(l.z,{variant:"ghost",size:"sm",children:a.jsx(M.Z,{className:"h-4 w-4"})})]})})]},e.id)})})]})})})}),0===V.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(FileTemplate,{className:"mx-auto h-12 w-12 text-gray-400"}),a.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"没有找到模板"}),a.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"尝试调整搜索条件或创建新模板"}),a.jsx("div",{className:"mt-6",children:(0,a.jsxs)(l.z,{children:[a.jsx(j.Z,{className:"mr-2 h-4 w-4"}),"创建新模板"]})})]})]})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>c});var a=t(326);t(7577);var r=t(9360),l=t(9310);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:s,...t}){return a.jsx("div",{className:(0,l.cn)(i({variant:s}),e),...t})}},2643:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>c,SZ:()=>d,Zb:()=>i,aY:()=>x,ll:()=>n});var a=t(326),r=t(7577),l=t(9310);let i=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let n=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let x=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},8715:(e,s,t)=>{"use strict";t.d(s,{I:()=>c});var a=t(326),r=t(7577),l=t(1135),i=t(1009);let c=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:function(...e){return(0,i.m6)((0,l.W)(e))}("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));c.displayName="Input"},8998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1540:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1137:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},924:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},9389:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},9216:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},3855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3734:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},765:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]])},3468:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},2099:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`E:\rjkf\tb-0704-V\app\templates\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,555,253],()=>t(5990));module.exports=a})();