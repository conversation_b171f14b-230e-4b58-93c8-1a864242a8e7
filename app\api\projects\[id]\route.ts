import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase()
    const { id } = params
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      )
    }
    
    const project = await db.collection('projects').findOne({ _id: new ObjectId(id) })
    
    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: project
    })
  } catch (error) {
    console.error('获取项目详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目详情失败' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase()
    const { id } = params
    const body = await request.json()
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      )
    }
    
    // 检查项目是否存在
    const existingProject = await db.collection('projects').findOne({ _id: new ObjectId(id) })
    if (!existingProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }
    
    // 如果更新招标编号，检查是否与其他项目冲突
    if (body.tenderNumber && body.tenderNumber !== existingProject.tenderNumber) {
      const duplicateProject = await db.collection('projects').findOne({
        tenderNumber: body.tenderNumber,
        _id: { $ne: new ObjectId(id) }
      })
      if (duplicateProject) {
        return NextResponse.json(
          { success: false, error: '招标编号已存在' },
          { status: 400 }
        )
      }
    }
    
    const updateData = {
      ...body,
      updatedAt: new Date(),
      updatedBy: 'current-user' // TODO: 从认证中获取用户信息
    }
    
    const result = await db.collection('projects').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    )
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }
    
    // 获取更新后的项目
    const updatedProject = await db.collection('projects').findOne({ _id: new ObjectId(id) })
    
    return NextResponse.json({
      success: true,
      data: updatedProject
    })
  } catch (error) {
    console.error('更新项目失败:', error)
    return NextResponse.json(
      { success: false, error: '更新项目失败' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase()
    const { id } = params
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      )
    }
    
    // 检查项目是否存在
    const existingProject = await db.collection('projects').findOne({ _id: new ObjectId(id) })
    if (!existingProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }
    
    // 删除项目相关的文档
    await db.collection('documents').deleteMany({ projectId: id })
    
    // 删除项目
    const result = await db.collection('projects').deleteOne({ _id: new ObjectId(id) })
    
    if (result.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: '删除项目失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: '项目删除成功'
    })
  } catch (error) {
    console.error('删除项目失败:', error)
    return NextResponse.json(
      { success: false, error: '删除项目失败' },
      { status: 500 }
    )
  }
}
