import axios from 'axios'

interface APIYIConfig {
  apiKey: string
  baseURL: string
}

interface APIYIRequest {
  model: string
  messages: {
    role: 'system' | 'user' | 'assistant'
    content: string
  }[]
  temperature?: number
  max_tokens?: number
}

interface APIYIResponse {
  id: string
  object: string
  created: number
  model: string
  choices: {
    index: number
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }[]
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

class APIYIService {
  private config: APIYIConfig

  constructor() {
    this.config = {
      apiKey: process.env.APIYI_API_KEY || '',
      baseURL: 'https://api.apiyi.com/v1'
    }

    if (!this.config.apiKey) {
      throw new Error('APIYI_API_KEY is required')
    }
  }

  async chat(request: APIYIRequest): Promise<APIYIResponse> {
    try {
      const response = await axios.post(
        `${this.config.baseURL}/chat/completions`,
        request,
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('APIYI API error:', error)
      throw new Error('Failed to call APIYI API')
    }
  }

  // 提取文档关键信息
  async extractKeyInformation(content: string): Promise<{
    entities: string[]
    keywords: string[]
    categories: string[]
    importance: 'high' | 'medium' | 'low'
  }> {
    const request: APIYIRequest = {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `你是一个专业的文档信息提取专家。请从提供的文档中提取关键信息。

请按照以下JSON格式返回结果：
{
  "entities": ["实体1", "实体2", ...],
  "keywords": ["关键词1", "关键词2", ...],
  "categories": ["类别1", "类别2", ...],
  "importance": "high|medium|low"
}`
        },
        {
          role: 'user',
          content: `请从以下文档中提取关键信息：\n\n${content}`
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    }

    const response = await this.chat(request)
    const result = response.choices[0].message.content

    try {
      return JSON.parse(result)
    } catch (error) {
      throw new Error('Failed to parse information extraction result')
    }
  }

  // 生成技术方案
  async generateTechnicalSolution(
    projectType: string,
    requirements: string[],
    constraints: string[]
  ): Promise<string> {
    const request: APIYIRequest = {
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `你是一个资深的工程技术专家。请根据项目要求生成详细的技术方案。

技术方案应包括：
1. 技术路线和方法
2. 关键技术要点
3. 实施步骤和时间安排
4. 质量控制措施
5. 风险控制和应对措施
6. 资源配置方案

请确保方案切实可行，技术先进，符合相关规范标准。`
        },
        {
          role: 'user',
          content: `请为以下项目生成技术方案：

项目类型：${projectType}

项目要求：
${requirements.join('\n')}

约束条件：
${constraints.join('\n')}

请生成详细的技术方案。`
        }
      ],
      temperature: 0.7,
      max_tokens: 6000
    }

    const response = await this.chat(request)
    return response.choices[0].message.content
  }

  // 生成施工组织设计
  async generateConstructionPlan(
    projectInfo: any,
    siteConditions: any,
    resources: any
  ): Promise<string> {
    const request: APIYIRequest = {
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `你是一个专业的施工组织设计专家。请根据项目信息生成详细的施工组织设计。

施工组织设计应包括：
1. 工程概况和特点分析
2. 施工部署和总体安排
3. 主要施工方法和工艺
4. 施工进度计划
5. 资源配置计划
6. 施工现场平面布置
7. 质量保证措施
8. 安全文明施工措施
9. 环境保护措施

请确保内容详实，方案可行，符合施工规范要求。`
        },
        {
          role: 'user',
          content: `请生成施工组织设计：

项目信息：
${JSON.stringify(projectInfo, null, 2)}

现场条件：
${JSON.stringify(siteConditions, null, 2)}

资源情况：
${JSON.stringify(resources, null, 2)}

请生成详细的施工组织设计。`
        }
      ],
      temperature: 0.7,
      max_tokens: 8000
    }

    const response = await this.chat(request)
    return response.choices[0].message.content
  }

  // 质量检查和建议
  async reviewContent(content: string, criteria: string[]): Promise<{
    score: number
    issues: string[]
    suggestions: string[]
    improvements: string[]
  }> {
    const request: APIYIRequest = {
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `你是一个专业的文档质量审查专家。请对提供的内容进行全面的质量评估。

请按照以下JSON格式返回评估结果：
{
  "score": 85,
  "issues": ["问题1", "问题2", ...],
  "suggestions": ["建议1", "建议2", ...],
  "improvements": ["改进点1", "改进点2", ...]
}`
        },
        {
          role: 'user',
          content: `请评估以下内容的质量：

评估标准：
${criteria.join('\n')}

内容：
${content}

请提供详细的质量评估。`
        }
      ],
      temperature: 0.3,
      max_tokens: 3000
    }

    const response = await this.chat(request)
    const result = response.choices[0].message.content

    try {
      return JSON.parse(result)
    } catch (error) {
      throw new Error('Failed to parse content review result')
    }
  }
}

export default new APIYIService()
