/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
  env: {
    MONGODB_URI: process.env.MONGODB_URI,
    NEO4J_URI: process.env.NEO4J_URI,
    NEO4J_USERNAME: process.env.NEO4J_USERNAME,
    NEO4J_PASSWORD: process.env.NEO4J_PASSWORD,
    DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY,
    APIYI_API_KEY: process.env.APIYI_API_KEY,
  },
}

module.exports = nextConfig
