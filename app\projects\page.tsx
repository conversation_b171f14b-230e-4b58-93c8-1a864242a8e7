'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Calendar,
  Users,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Download
} from 'lucide-react'

interface Project {
  id: string
  name: string
  description: string
  status: 'active' | 'completed' | 'pending' | 'overdue'
  deadline: string
  progress: number
  tenderNumber: string
  budget: string
  documents: number
  team: string[]
  createdAt: string
  updatedAt: string
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 模拟加载项目数据
    setTimeout(() => {
      setProjects([
        {
          id: '1',
          name: '某市政道路建设项目',
          description: '城市主干道改造升级工程，包括路面重铺、排水系统改造等',
          status: 'active',
          deadline: '2024-07-15',
          progress: 75,
          tenderNumber: 'SZ-2024-001',
          budget: '5000万元',
          documents: 12,
          team: ['张工程师', '李项目经理', '王技术员'],
          createdAt: '2024-06-01',
          updatedAt: '2024-07-02'
        },
        {
          id: '2',
          name: '办公楼装修工程',
          description: '企业总部办公楼内部装修改造项目',
          status: 'active',
          deadline: '2024-07-20',
          progress: 45,
          tenderNumber: 'BG-2024-002',
          budget: '800万元',
          documents: 8,
          team: ['陈设计师', '刘监理'],
          createdAt: '2024-06-10',
          updatedAt: '2024-07-01'
        },
        {
          id: '3',
          name: 'IT设备采购项目',
          description: '政府机关办公设备批量采购',
          status: 'completed',
          deadline: '2024-06-30',
          progress: 100,
          tenderNumber: 'IT-2024-003',
          budget: '200万元',
          documents: 15,
          team: ['赵采购员', '钱技术专家'],
          createdAt: '2024-05-15',
          updatedAt: '2024-06-30'
        },
        {
          id: '4',
          name: '学校设备采购',
          description: '中小学教学设备更新采购项目',
          status: 'pending',
          deadline: '2024-07-25',
          progress: 20,
          tenderNumber: 'EDU-2024-004',
          budget: '1500万元',
          documents: 5,
          team: ['孙教育专家'],
          createdAt: '2024-06-20',
          updatedAt: '2024-06-25'
        },
        {
          id: '5',
          name: '医院设备更新',
          description: '市人民医院医疗设备采购更新项目',
          status: 'overdue',
          deadline: '2024-06-25',
          progress: 60,
          tenderNumber: 'MED-2024-005',
          budget: '3000万元',
          documents: 20,
          team: ['周医疗专家', '吴采购经理', '郑技术顾问'],
          createdAt: '2024-05-01',
          updatedAt: '2024-06-20'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getStatusInfo = (status: string) => {
    const statusMap = {
      active: { 
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300', 
        label: '进行中',
        icon: Clock
      },
      completed: { 
        color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300', 
        label: '已完成',
        icon: CheckCircle
      },
      pending: { 
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300', 
        label: '待开始',
        icon: Calendar
      },
      overdue: { 
        color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300', 
        label: '已逾期',
        icon: AlertCircle
      }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.pending
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.tenderNumber.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus
    return matchesSearch && matchesFilter
  })

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    if (progress >= 30) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">加载项目数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">项目管理</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                管理您的投标项目，跟踪进度和截止日期
              </p>
            </div>
            <Link href="/projects/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建新项目
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和筛选 */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索项目名称或招标编号..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
            >
              <option value="all">全部状态</option>
              <option value="active">进行中</option>
              <option value="pending">待开始</option>
              <option value="completed">已完成</option>
              <option value="overdue">已逾期</option>
            </select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              更多筛选
            </Button>
          </div>
        </div>

        {/* 项目统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">进行中</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">已完成</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'completed').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">待开始</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'pending').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">已逾期</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'overdue').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 项目列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProjects.map((project) => {
            const statusInfo = getStatusInfo(project.status)
            const StatusIcon = statusInfo.icon
            
            return (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{project.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {project.description}
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge className={statusInfo.color}>
                      <StatusIcon className="mr-1 h-3 w-3" />
                      {statusInfo.label}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {project.tenderNumber}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* 进度条 */}
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>完成进度</span>
                        <span>{project.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getProgressColor(project.progress)}`}
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* 项目信息 */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">截止日期</p>
                        <p className="font-medium">{project.deadline}</p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">预算</p>
                        <p className="font-medium">{project.budget}</p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">文档数量</p>
                        <p className="font-medium">{project.documents} 个</p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">团队成员</p>
                        <p className="font-medium">{project.team.length} 人</p>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-2 pt-4 border-t">
                      <Link href={`/projects/${project.id}/preview`} className="flex-1">
                        <Button variant="default" size="sm" className="w-full">
                          <Eye className="mr-2 h-4 w-4" />
                          预览
                        </Button>
                      </Link>
                      <Link href={`/projects/${project.id}`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              没有找到项目
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              尝试调整搜索条件或创建新项目
            </p>
            <div className="mt-6">
              <Link href="/projects/new">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  创建新项目
                </Button>
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
