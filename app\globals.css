@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* 自定义滚动条 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }

  /* 编辑器样式 */
  .editor-content {
    @apply prose prose-sm sm:prose lg:prose-lg xl:prose-xl mx-auto focus:outline-none;
  }

  .editor-content h1 {
    @apply text-3xl font-bold mb-4;
  }

  .editor-content h2 {
    @apply text-2xl font-semibold mb-3;
  }

  .editor-content h3 {
    @apply text-xl font-medium mb-2;
  }

  .editor-content p {
    @apply mb-4 leading-relaxed;
  }

  .editor-content ul, .editor-content ol {
    @apply mb-4 pl-6;
  }

  .editor-content li {
    @apply mb-1;
  }

  .editor-content blockquote {
    @apply border-l-4 border-primary pl-4 italic my-4;
  }

  .editor-content table {
    @apply w-full border-collapse border border-border mb-4;
  }

  .editor-content th, .editor-content td {
    @apply border border-border px-3 py-2 text-left;
  }

  .editor-content th {
    @apply bg-muted font-semibold;
  }

  /* 卡片阴影 */
  .card-shadow {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* 动画效果 */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .bounce-in {
    animation: bounceIn 0.5s ease-out;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  /* 文件拖拽区域 */
  .dropzone {
    @apply border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center transition-colors;
  }

  .dropzone.active {
    @apply border-primary bg-primary/5;
  }

  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-indicator.draft {
    @apply bg-gray-100 text-gray-800;
  }

  .status-indicator.in-progress {
    @apply bg-blue-100 text-blue-800;
  }

  .status-indicator.review {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-indicator.completed {
    @apply bg-green-100 text-green-800;
  }

  .status-indicator.archived {
    @apply bg-gray-100 text-gray-600;
  }

  /* 深色模式下的状态指示器 */
  .dark .status-indicator.draft {
    @apply bg-gray-800 text-gray-200;
  }

  .dark .status-indicator.in-progress {
    @apply bg-blue-900 text-blue-200;
  }

  .dark .status-indicator.review {
    @apply bg-yellow-900 text-yellow-200;
  }

  .dark .status-indicator.completed {
    @apply bg-green-900 text-green-200;
  }

  .dark .status-indicator.archived {
    @apply bg-gray-800 text-gray-400;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
}
