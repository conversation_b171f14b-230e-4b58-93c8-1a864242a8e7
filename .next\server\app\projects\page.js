(()=>{var e={};e.id=895,e.ids=[895],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6540:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>c}),r(5673),r(6070),r(5866);var t=r(3191),a=r(8716),l=r(7922),i=r.n(l),d=r(5231),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let c=["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5673)),"E:\\rjkf\\tb-0704-V\\app\\projects\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6070)),"E:\\rjkf\\tb-0704-V\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],x=["E:\\rjkf\\tb-0704-V\\app\\projects\\page.tsx"],o="/projects/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8804:(e,s,r)=>{Promise.resolve().then(r.bind(r,4134))},4134:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(326),a=r(7577),l=r(434),i=r(772),d=r(2643),n=r(567),c=r(8715),x=r(8998),o=r(4659),m=r(7358),h=r(7888),u=r(3855),g=r(8307),p=r(1137),j=r(9216),f=r(2714),y=r(3),b=r(1540),v=r(6283);function N(){let[e,s]=(0,a.useState)([]),[r,N]=(0,a.useState)(""),[k,w]=(0,a.useState)("all"),[Z,C]=(0,a.useState)(!0),z=e=>{let s={active:{color:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",label:"进行中",icon:x.Z},completed:{color:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",label:"已完成",icon:o.Z},pending:{color:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",label:"待开始",icon:m.Z},overdue:{color:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",label:"已逾期",icon:h.Z}};return s[e]||s.pending},P=e.filter(e=>{let s=e.name.toLowerCase().includes(r.toLowerCase())||e.tenderNumber.toLowerCase().includes(r.toLowerCase()),t="all"===k||e.status===k;return s&&t}),_=e=>e>=80?"bg-green-500":e>=50?"bg-blue-500":e>=30?"bg-yellow-500":"bg-red-500";return Z?t.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"加载项目数据中..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[t.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"项目管理"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"管理您的投标项目，跟踪进度和截止日期"})]}),t.jsx(l.default,{href:"/projects/new",children:(0,t.jsxs)(i.z,{children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"创建新项目"]})})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[t.jsx(g.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),t.jsx(c.I,{placeholder:"搜索项目名称或招标编号...",value:r,onChange:e=>N(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:k,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600",children:[t.jsx("option",{value:"all",children:"全部状态"}),t.jsx("option",{value:"active",children:"进行中"}),t.jsx("option",{value:"pending",children:"待开始"}),t.jsx("option",{value:"completed",children:"已完成"}),t.jsx("option",{value:"overdue",children:"已逾期"})]}),(0,t.jsxs)(i.z,{variant:"outline",children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"更多筛选"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[t.jsx(d.Zb,{children:t.jsx(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:t.jsx(x.Z,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"进行中"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"active"===e.status).length})]})]})})}),t.jsx(d.Zb,{children:t.jsx(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx(o.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"已完成"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"completed"===e.status).length})]})]})})}),t.jsx(d.Zb,{children:t.jsx(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:t.jsx(m.Z,{className:"h-6 w-6 text-yellow-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"待开始"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"pending"===e.status).length})]})]})})}),t.jsx(d.Zb,{children:t.jsx(d.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:t.jsx(h.Z,{className:"h-6 w-6 text-red-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"已逾期"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.filter(e=>"overdue"===e.status).length})]})]})})})]}),t.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:P.map(e=>{let s=z(e.status),r=s.icon;return(0,t.jsxs)(d.Zb,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsxs)(d.Ol,{children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx(d.ll,{className:"text-lg",children:e.name}),t.jsx(d.SZ,{className:"mt-1",children:e.description})]}),t.jsx(i.z,{variant:"ghost",size:"icon",children:t.jsx(j.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,t.jsxs)(n.C,{className:s.color,children:[t.jsx(r,{className:"mr-1 h-3 w-3"}),s.label]}),t.jsx("span",{className:"text-sm text-gray-500",children:e.tenderNumber})]})]}),t.jsx(d.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[t.jsx("span",{children:"完成进度"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:`h-2 rounded-full ${_(e.progress)}`,style:{width:`${e.progress}%`}})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"截止日期"}),t.jsx("p",{className:"font-medium",children:e.deadline})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"预算"}),t.jsx("p",{className:"font-medium",children:e.budget})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"文档数量"}),(0,t.jsxs)("p",{className:"font-medium",children:[e.documents," 个"]})]}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"团队成员"}),(0,t.jsxs)("p",{className:"font-medium",children:[e.team.length," 人"]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-4 border-t",children:[t.jsx(l.default,{href:`/projects/${e.id}`,className:"flex-1",children:(0,t.jsxs)(i.z,{variant:"outline",size:"sm",className:"w-full",children:[t.jsx(f.Z,{className:"mr-2 h-4 w-4"}),"查看"]})}),t.jsx(l.default,{href:`/projects/${e.id}/edit`,className:"flex-1",children:(0,t.jsxs)(i.z,{variant:"outline",size:"sm",className:"w-full",children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"编辑"]})}),t.jsx(i.z,{variant:"outline",size:"sm",children:t.jsx(b.Z,{className:"h-4 w-4"})})]})]})})]},e.id)})}),0===P.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx(v.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),t.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"没有找到项目"}),t.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"尝试调整搜索条件或创建新项目"}),t.jsx("div",{className:"mt-6",children:t.jsx(l.default,{href:"/projects/new",children:(0,t.jsxs)(i.z,{children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"创建新项目"]})})})]})]})]})}},567:(e,s,r)=>{"use strict";r.d(s,{C:()=>d});var t=r(326);r(7577);var a=r(9360),l=r(9310);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...r}){return t.jsx("div",{className:(0,l.cn)(i({variant:s}),e),...r})}},2643:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>d,SZ:()=>c,Zb:()=>i,aY:()=>x,ll:()=>n});var t=r(326),a=r(7577),l=r(9310);let i=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let d=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let n=a.forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let x=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},8715:(e,s,r)=>{"use strict";r.d(s,{I:()=>d});var t=r(326),a=r(7577),l=r(1135),i=r(1009);let d=a.forwardRef(({className:e,type:s,...r},a)=>t.jsx("input",{type:s,className:function(...e){return(0,i.m6)((0,l.W)(e))}("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));d.displayName="Input"},7888:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7358:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},4659:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8998:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1540:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2714:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1137:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},9216:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},3855:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5673:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`E:\rjkf\tb-0704-V\app\projects\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[276,555,253],()=>r(6540));module.exports=t})();