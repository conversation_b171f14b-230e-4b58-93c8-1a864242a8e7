import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    
    // 构建查询条件
    const query: any = {}
    if (status && status !== 'all') {
      query.status = status
    }
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { tenderNumber: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }
    
    // 分页查询
    const skip = (page - 1) * limit
    const projects = await db.collection('projects')
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray()
    
    const total = await db.collection('projects').countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: {
        projects,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取项目列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目列表失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const body = await request.json()
    
    // 验证必填字段
    const { name, description, deadline, budget, tenderNumber } = body
    if (!name || !description || !deadline || !tenderNumber) {
      return NextResponse.json(
        { success: false, error: '缺少必填字段' },
        { status: 400 }
      )
    }
    
    // 检查招标编号是否已存在
    const existingProject = await db.collection('projects').findOne({ tenderNumber })
    if (existingProject) {
      return NextResponse.json(
        { success: false, error: '招标编号已存在' },
        { status: 400 }
      )
    }
    
    const project = {
      ...body,
      status: body.status || 'pending',
      progress: 0,
      documents: [],
      team: body.team || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user' // TODO: 从认证中获取用户信息
    }
    
    const result = await db.collection('projects').insertOne(project)
    
    return NextResponse.json({
      success: true,
      data: {
        id: result.insertedId,
        ...project
      }
    }, { status: 201 })
  } catch (error) {
    console.error('创建项目失败:', error)
    return NextResponse.json(
      { success: false, error: '创建项目失败' },
      { status: 500 }
    )
  }
}
