"use strict";(()=>{var e={};e.id=361,e.ids=[361],e.modules={8013:e=>{e.exports=require("mongodb")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5580:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>O,patchFetch:()=>b,requestAsyncStorage:()=>x,routeModule:()=>j,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{DELETE:()=>l,GET:()=>d,PUT:()=>p});var n=s(9303),o=s(8716),c=s(670),a=s(7070),i=s(2021),u=s(8013);async function d(e,{params:t}){try{let{db:e}=await (0,i.vO)(),{id:s}=t;if(!u.ObjectId.isValid(s))return a.NextResponse.json({success:!1,error:"无效的项目ID"},{status:400});let r=await e.collection("projects").findOne({_id:new u.ObjectId(s)});if(!r)return a.NextResponse.json({success:!1,error:"项目不存在"},{status:404});return a.NextResponse.json({success:!0,data:r})}catch(e){return console.error("获取项目详情失败:",e),a.NextResponse.json({success:!1,error:"获取项目详情失败"},{status:500})}}async function p(e,{params:t}){try{let{db:s}=await (0,i.vO)(),{id:r}=t,n=await e.json();if(!u.ObjectId.isValid(r))return a.NextResponse.json({success:!1,error:"无效的项目ID"},{status:400});let o=await s.collection("projects").findOne({_id:new u.ObjectId(r)});if(!o)return a.NextResponse.json({success:!1,error:"项目不存在"},{status:404});if(n.tenderNumber&&n.tenderNumber!==o.tenderNumber&&await s.collection("projects").findOne({tenderNumber:n.tenderNumber,_id:{$ne:new u.ObjectId(r)}}))return a.NextResponse.json({success:!1,error:"招标编号已存在"},{status:400});let c={...n,updatedAt:new Date,updatedBy:"current-user"},d=await s.collection("projects").updateOne({_id:new u.ObjectId(r)},{$set:c});if(0===d.matchedCount)return a.NextResponse.json({success:!1,error:"项目不存在"},{status:404});let p=await s.collection("projects").findOne({_id:new u.ObjectId(r)});return a.NextResponse.json({success:!0,data:p})}catch(e){return console.error("更新项目失败:",e),a.NextResponse.json({success:!1,error:"更新项目失败"},{status:500})}}async function l(e,{params:t}){try{let{db:e}=await (0,i.vO)(),{id:s}=t;if(!u.ObjectId.isValid(s))return a.NextResponse.json({success:!1,error:"无效的项目ID"},{status:400});if(!await e.collection("projects").findOne({_id:new u.ObjectId(s)}))return a.NextResponse.json({success:!1,error:"项目不存在"},{status:404});await e.collection("documents").deleteMany({projectId:s});let r=await e.collection("projects").deleteOne({_id:new u.ObjectId(s)});if(0===r.deletedCount)return a.NextResponse.json({success:!1,error:"删除项目失败"},{status:500});return a.NextResponse.json({success:!0,message:"项目删除成功"})}catch(e){return console.error("删除项目失败:",e),a.NextResponse.json({success:!1,error:"删除项目失败"},{status:500})}}let j=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"E:\\rjkf\\tb-0704-V\\app\\api\\projects\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:x,staticGenerationAsyncStorage:w,serverHooks:f}=j,O="/api/projects/[id]/route";function b(){return(0,c.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},2021:(e,t,s)=>{let r;async function n(){let e=await r,t=e.db("tender-editor");return{client:e,db:t}}s.d(t,{vO:()=>n}),r=new(s(8013)).MongoClient("mongodb://localhost:27017/tender-editor",{}).connect()}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,972],()=>s(5580));module.exports=r})();