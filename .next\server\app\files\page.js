(()=>{var a={};a.id=357,a.ids=[357],a.modules={2934:a=>{"use strict";a.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:a=>{"use strict";a.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:a=>{"use strict";a.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4590:(a,i,e)=>{"use strict";e.r(i),e.d(i,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>s,routeModule:()=>x,tree:()=>r}),e(6151),e(6070),e(5866);var t=e(3191),n=e(8716),p=e(7922),o=e.n(p),l=e(5231),c={};for(let a in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(a)&&(c[a]=()=>l[a]);e.d(i,c);let r=["",{children:["files",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(e.bind(e,6151)),"E:\\rjkf\\tb-0704-V\\app\\files\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(e.bind(e,6070)),"E:\\rjkf\\tb-0704-V\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(e.t.bind(e,5866,23)),"next/dist/client/components/not-found-error"]}],s=["E:\\rjkf\\tb-0704-V\\app\\files\\page.tsx"],d="/files/page",m={require:e,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/files/page",pathname:"/files",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:r}})},7228:(a,i,e)=>{Promise.resolve().then(e.bind(e,8812))},8812:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>aE});var t=e(326),n=e(7577),p=e(8439);function o(a,i,e,t){return new(e||(e=Promise))(function(n,p){function o(a){try{c(t.next(a))}catch(a){p(a)}}function l(a){try{c(t.throw(a))}catch(a){p(a)}}function c(a){var i;a.done?n(a.value):((i=a.value)instanceof e?i:new e(function(a){a(i)})).then(o,l)}c((t=t.apply(a,i||[])).next())})}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;let l=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function c(a,i,e){let t=function(a){let{name:i}=a;if(i&&-1!==i.lastIndexOf(".")&&!a.type){let e=i.split(".").pop().toLowerCase(),t=l.get(e);t&&Object.defineProperty(a,"type",{value:t,writable:!1,configurable:!1,enumerable:!0})}return a}(a),{webkitRelativePath:n}=a,p="string"==typeof i?i:"string"==typeof n&&n.length>0?n:`./${a.name}`;return"string"!=typeof t.path&&r(t,"path",p),void 0!==e&&Object.defineProperty(t,"handle",{value:e,writable:!1,configurable:!1,enumerable:!0}),r(t,"relativePath",p),t}function r(a,i,e){Object.defineProperty(a,i,{value:e,writable:!1,configurable:!1,enumerable:!0})}let s=[".DS_Store","Thumbs.db"];function d(a){return"object"==typeof a&&null!==a}function m(a){return a.filter(a=>-1===s.indexOf(a.name))}function x(a){if(null===a)return[];let i=[];for(let e=0;e<a.length;e++){let t=a[e];i.push(t)}return i}function v(a){if("function"!=typeof a.webkitGetAsEntry)return u(a);let i=a.webkitGetAsEntry();return i&&i.isDirectory?g(i):u(a,i)}function u(a,i){return o(this,void 0,void 0,function*(){var e;if(globalThis.isSecureContext&&"function"==typeof a.getAsFileSystemHandle){let i=yield a.getAsFileSystemHandle();if(null===i)throw Error(`${a} is not a File`);if(void 0!==i){let a=yield i.getFile();return a.handle=i,c(a)}}let t=a.getAsFile();if(!t)throw Error(`${a} is not a File`);return c(t,null!==(e=null==i?void 0:i.fullPath)&&void 0!==e?e:void 0)})}function f(a){return o(this,void 0,void 0,function*(){return a.isDirectory?g(a):function(a){return o(this,void 0,void 0,function*(){return new Promise((i,e)=>{a.file(e=>{i(c(e,a.fullPath))},a=>{e(a)})})})}(a)})}function g(a){let i=a.createReader();return new Promise((a,e)=>{let t=[];!function n(){i.readEntries(i=>o(this,void 0,void 0,function*(){if(i.length){let a=Promise.all(i.map(f));t.push(a),n()}else try{let i=yield Promise.all(t);a(i)}catch(a){e(a)}}),a=>{e(a)})}()})}var h=e(4191);function b(a){return function(a){if(Array.isArray(a))return N(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||z(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(a,i){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);i&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),e.push.apply(e,t)}return e}function w(a){for(var i=1;i<arguments.length;i++){var e=null!=arguments[i]?arguments[i]:{};i%2?y(Object(e),!0).forEach(function(i){j(a,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):y(Object(e)).forEach(function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(e,i))})}return a}function j(a,i,e){return i in a?Object.defineProperty(a,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[i]=e,a}function k(a,i){return function(a){if(Array.isArray(a))return a}(a)||function(a,i){var e,t,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var p=[],o=!0,l=!1;try{for(n=n.call(a);!(o=(e=n.next()).done)&&(p.push(e.value),!i||p.length!==i);o=!0);}catch(a){l=!0,t=a}finally{try{o||null==n.return||n.return()}finally{if(l)throw t}}return p}}(a,i)||z(a,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(a,i){if(a){if("string"==typeof a)return N(a,i);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return N(a,i)}}function N(a,i){(null==i||i>a.length)&&(i=a.length);for(var e=0,t=Array(i);e<i;e++)t[e]=a[e];return t}var D="function"==typeof h?h:h.default,O=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",i=a.split(","),e=i.length>1?"one of ".concat(i.join(", ")):i[0];return{code:"file-invalid-type",message:"File type must be ".concat(e)}},E=function(a){return{code:"file-too-large",message:"File is larger than ".concat(a," ").concat(1===a?"byte":"bytes")}},A=function(a){return{code:"file-too-small",message:"File is smaller than ".concat(a," ").concat(1===a?"byte":"bytes")}},P={code:"too-many-files",message:"Too many files"};function q(a,i){var e="application/x-moz-file"===a.type||D(a,i);return[e,e?null:O(i)]}function C(a,i,e){if(F(a.size)){if(F(i)&&F(e)){if(a.size>e)return[!1,E(e)];if(a.size<i)return[!1,A(i)]}else if(F(i)&&a.size<i)return[!1,A(i)];else if(F(e)&&a.size>e)return[!1,E(e)]}return[!0,null]}function F(a){return null!=a}function S(a){return"function"==typeof a.isPropagationStopped?a.isPropagationStopped():void 0!==a.cancelBubble&&a.cancelBubble}function Z(a){return a.dataTransfer?Array.prototype.some.call(a.dataTransfer.types,function(a){return"Files"===a||"application/x-moz-file"===a}):!!a.target&&!!a.target.files}function M(a){a.preventDefault()}function R(){for(var a=arguments.length,i=Array(a),e=0;e<a;e++)i[e]=arguments[e];return function(a){for(var e=arguments.length,t=Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];return i.some(function(i){return!S(a)&&i&&i.apply(void 0,[a].concat(t)),S(a)})}}function _(a){return"audio/*"===a||"video/*"===a||"image/*"===a||"text/*"===a||"application/*"===a||/\w+\/[-+.\w]+/g.test(a)}function T(a){return/^.*\.[\w]+$/.test(a)}var I=["children"],L=["open"],B=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],H=["refKey","onChange","onClick"];function W(a,i){return function(a){if(Array.isArray(a))return a}(a)||function(a,i){var e,t,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var p=[],o=!0,l=!1;try{for(n=n.call(a);!(o=(e=n.next()).done)&&(p.push(e.value),!i||p.length!==i);o=!0);}catch(a){l=!0,t=a}finally{try{o||null==n.return||n.return()}finally{if(l)throw t}}return p}}(a,i)||K(a,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(a,i){if(a){if("string"==typeof a)return Y(a,i);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return Y(a,i)}}function Y(a,i){(null==i||i>a.length)&&(i=a.length);for(var e=0,t=Array(i);e<i;e++)t[e]=a[e];return t}function $(a,i){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);i&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),e.push.apply(e,t)}return e}function U(a){for(var i=1;i<arguments.length;i++){var e=null!=arguments[i]?arguments[i]:{};i%2?$(Object(e),!0).forEach(function(i){V(a,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):$(Object(e)).forEach(function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(e,i))})}return a}function V(a,i,e){return i in a?Object.defineProperty(a,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[i]=e,a}function G(a,i){if(null==a)return{};var e,t,n=function(a,i){if(null==a)return{};var e,t,n={},p=Object.keys(a);for(t=0;t<p.length;t++)e=p[t],i.indexOf(e)>=0||(n[e]=a[e]);return n}(a,i);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(a);for(t=0;t<p.length;t++)e=p[t],!(i.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(a,e)&&(n[e]=a[e])}return n}var X=(0,n.forwardRef)(function(a,i){var e=a.children,t=aa(G(a,I)),p=t.open,o=G(t,L);return(0,n.useImperativeHandle)(i,function(){return{open:p}},[p]),n.createElement(n.Fragment,null,e(U(U({},o),{},{open:p})))});X.displayName="Dropzone";var J={disabled:!1,getFilesFromEvent:function(a){return o(this,void 0,void 0,function*(){return d(a)&&d(a.dataTransfer)?function(a,i){return o(this,void 0,void 0,function*(){if(a.items){let e=x(a.items).filter(a=>"file"===a.kind);return"drop"!==i?e:m(function a(i){return i.reduce((i,e)=>[...i,...Array.isArray(e)?a(e):[e]],[])}((yield Promise.all(e.map(v)))))}return m(x(a.files).map(a=>c(a)))})}(a.dataTransfer,a.type):d(a)&&d(a.target)?x(a.target.files).map(a=>c(a)):Array.isArray(a)&&a.every(a=>"getFile"in a&&"function"==typeof a.getFile)?function(a){return o(this,void 0,void 0,function*(){return(yield Promise.all(a.map(a=>a.getFile()))).map(a=>c(a))})}(a):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};X.defaultProps=J,X.propTypes={children:p.func,accept:p.objectOf(p.arrayOf(p.string)),multiple:p.bool,preventDropOnDocument:p.bool,noClick:p.bool,noKeyboard:p.bool,noDrag:p.bool,noDragEventsBubbling:p.bool,minSize:p.number,maxSize:p.number,maxFiles:p.number,disabled:p.bool,getFilesFromEvent:p.func,onFileDialogCancel:p.func,onFileDialogOpen:p.func,useFsAccessApi:p.bool,autoFocus:p.bool,onDragEnter:p.func,onDragLeave:p.func,onDragOver:p.func,onDrop:p.func,onDropAccepted:p.func,onDropRejected:p.func,onError:p.func,validator:p.func};var Q={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function aa(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=U(U({},J),a),e=i.accept,t=i.disabled,p=i.getFilesFromEvent,o=i.maxSize,l=i.minSize,c=i.multiple,r=i.maxFiles,s=i.onDragEnter,d=i.onDragLeave,m=i.onDragOver,x=i.onDrop,v=i.onDropAccepted,u=i.onDropRejected,f=i.onFileDialogCancel,g=i.onFileDialogOpen,h=i.useFsAccessApi,y=i.autoFocus,z=i.preventDropOnDocument,N=i.noClick,D=i.noKeyboard,O=i.noDrag,E=i.noDragEventsBubbling,A=i.onError,I=i.validator,L=(0,n.useMemo)(function(){return function(a){if(F(a))return Object.entries(a).reduce(function(a,i){var e=k(i,2),t=e[0],n=e[1];return[].concat(b(a),[t],b(n))},[]).filter(function(a){return _(a)||T(a)}).join(",")}(e)},[e]),$=(0,n.useMemo)(function(){return F(e)?[{description:"Files",accept:Object.entries(e).filter(function(a){var i=k(a,2),e=i[0],t=i[1],n=!0;return _(e)||(console.warn('Skipped "'.concat(e,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(t)&&t.every(T)||(console.warn('Skipped "'.concat(e,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(a,i){var e=k(i,2),t=e[0],n=e[1];return w(w({},a),{},j({},t,n))},{})}]:e},[e]),X=(0,n.useMemo)(function(){return"function"==typeof g?g:ae},[g]),aa=(0,n.useMemo)(function(){return"function"==typeof f?f:ae},[f]),at=(0,n.useRef)(null),an=(0,n.useRef)(null),ap=W((0,n.useReducer)(ai,Q),2),ao=ap[0],al=ap[1],ac=ao.isFocused,ar=ao.isFileDialogActive,as=(0,n.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),ad=function(){!as.current&&ar&&setTimeout(function(){an.current&&!an.current.files.length&&(al({type:"closeDialog"}),aa())},300)};(0,n.useEffect)(function(){return window.addEventListener("focus",ad,!1),function(){window.removeEventListener("focus",ad,!1)}},[an,ar,aa,as]);var am=(0,n.useRef)([]),ax=function(a){at.current&&at.current.contains(a.target)||(a.preventDefault(),am.current=[])};(0,n.useEffect)(function(){return z&&(document.addEventListener("dragover",M,!1),document.addEventListener("drop",ax,!1)),function(){z&&(document.removeEventListener("dragover",M),document.removeEventListener("drop",ax))}},[at,z]),(0,n.useEffect)(function(){return!t&&y&&at.current&&at.current.focus(),function(){}},[at,y,t]);var av=(0,n.useCallback)(function(a){A?A(a):console.error(a)},[A]),au=(0,n.useCallback)(function(a){var i;a.preventDefault(),a.persist(),aE(a),am.current=[].concat(function(a){if(Array.isArray(a))return Y(a)}(i=am.current)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(i)||K(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[a.target]),Z(a)&&Promise.resolve(p(a)).then(function(i){if(!S(a)||E){var e,t,n,p,d,m,x,v,u=i.length,f=u>0&&(t=(e={files:i,accept:L,minSize:l,maxSize:o,multiple:c,maxFiles:r,validator:I}).files,n=e.accept,p=e.minSize,d=e.maxSize,m=e.multiple,x=e.maxFiles,v=e.validator,(!!m||!(t.length>1))&&(!m||!(x>=1)||!(t.length>x))&&t.every(function(a){var i=k(q(a,n),1)[0],e=k(C(a,p,d),1)[0],t=v?v(a):null;return i&&e&&!t}));al({isDragAccept:f,isDragReject:u>0&&!f,isDragActive:!0,type:"setDraggedFiles"}),s&&s(a)}}).catch(function(a){return av(a)})},[p,s,av,E,L,l,o,c,r,I]),af=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aE(a);var i=Z(a);if(i&&a.dataTransfer)try{a.dataTransfer.dropEffect="copy"}catch(a){}return i&&m&&m(a),!1},[m,E]),ag=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aE(a);var i=am.current.filter(function(a){return at.current&&at.current.contains(a)}),e=i.indexOf(a.target);-1!==e&&i.splice(e,1),am.current=i,!(i.length>0)&&(al({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Z(a)&&d&&d(a))},[at,d,E]),ah=(0,n.useCallback)(function(a,i){var e=[],t=[];a.forEach(function(a){var i=W(q(a,L),2),n=i[0],p=i[1],c=W(C(a,l,o),2),r=c[0],s=c[1],d=I?I(a):null;if(n&&r&&!d)e.push(a);else{var m=[p,s];d&&(m=m.concat(d)),t.push({file:a,errors:m.filter(function(a){return a})})}}),(!c&&e.length>1||c&&r>=1&&e.length>r)&&(e.forEach(function(a){t.push({file:a,errors:[P]})}),e.splice(0)),al({acceptedFiles:e,fileRejections:t,isDragReject:t.length>0,type:"setFiles"}),x&&x(e,t,i),t.length>0&&u&&u(t,i),e.length>0&&v&&v(e,i)},[al,c,L,l,o,r,x,v,u,I]),ab=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aE(a),am.current=[],Z(a)&&Promise.resolve(p(a)).then(function(i){(!S(a)||E)&&ah(i,a)}).catch(function(a){return av(a)}),al({type:"reset"})},[p,ah,av,E]),ay=(0,n.useCallback)(function(){if(as.current){al({type:"openDialog"}),X(),window.showOpenFilePicker({multiple:c,types:$}).then(function(a){return p(a)}).then(function(a){ah(a,null),al({type:"closeDialog"})}).catch(function(a){a instanceof DOMException&&("AbortError"===a.name||a.code===a.ABORT_ERR)?(aa(a),al({type:"closeDialog"})):a instanceof DOMException&&("SecurityError"===a.name||a.code===a.SECURITY_ERR)?(as.current=!1,an.current?(an.current.value=null,an.current.click()):av(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):av(a)});return}an.current&&(al({type:"openDialog"}),X(),an.current.value=null,an.current.click())},[al,X,aa,h,ah,av,$,c]),aw=(0,n.useCallback)(function(a){at.current&&at.current.isEqualNode(a.target)&&(" "===a.key||"Enter"===a.key||32===a.keyCode||13===a.keyCode)&&(a.preventDefault(),ay())},[at,ay]),aj=(0,n.useCallback)(function(){al({type:"focus"})},[]),ak=(0,n.useCallback)(function(){al({type:"blur"})},[]),az=(0,n.useCallback)(function(){N||(function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==a.indexOf("MSIE")||-1!==a.indexOf("Trident/")||-1!==a.indexOf("Edge/")}()?setTimeout(ay,0):ay())},[N,ay]),aN=function(a){return t?null:a},aD=function(a){return D?null:aN(a)},aO=function(a){return O?null:aN(a)},aE=function(a){E&&a.stopPropagation()},aA=(0,n.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,e=a.role,n=a.onKeyDown,p=a.onFocus,o=a.onBlur,l=a.onClick,c=a.onDragEnter,r=a.onDragOver,s=a.onDragLeave,d=a.onDrop,m=G(a,B);return U(U(V({onKeyDown:aD(R(n,aw)),onFocus:aD(R(p,aj)),onBlur:aD(R(o,ak)),onClick:aN(R(l,az)),onDragEnter:aO(R(c,au)),onDragOver:aO(R(r,af)),onDragLeave:aO(R(s,ag)),onDrop:aO(R(d,ab)),role:"string"==typeof e&&""!==e?e:"presentation"},void 0===i?"ref":i,at),t||D?{}:{tabIndex:0}),m)}},[at,aw,aj,ak,az,au,af,ag,ab,D,O,t]),aP=(0,n.useCallback)(function(a){a.stopPropagation()},[]),aq=(0,n.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,e=a.onChange,t=a.onClick,n=G(a,H);return U(U({},V({accept:L,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:aN(R(e,ab)),onClick:aN(R(t,aP)),tabIndex:-1},void 0===i?"ref":i,an)),n)}},[an,e,c,ab,t]);return U(U({},ao),{},{isFocused:ac&&!t,getRootProps:aA,getInputProps:aq,rootRef:at,inputRef:an,open:aN(ay)})}function ai(a,i){switch(i.type){case"focus":return U(U({},a),{},{isFocused:!0});case"blur":return U(U({},a),{},{isFocused:!1});case"openDialog":return U(U({},Q),{},{isFileDialogActive:!0});case"closeDialog":return U(U({},a),{},{isFileDialogActive:!1});case"setDraggedFiles":return U(U({},a),{},{isDragActive:i.isDragActive,isDragAccept:i.isDragAccept,isDragReject:i.isDragReject});case"setFiles":return U(U({},a),{},{acceptedFiles:i.acceptedFiles,fileRejections:i.fileRejections,isDragReject:i.isDragReject});case"reset":return U({},Q);default:return a}}function ae(){}var at=e(772),an=e(2643),ap=e(567),ao=e(8715),al=e(6283),ac=e(1709),ar=e(9572),as=e(9389),ad=e(924),am=e(3855),ax=e(3685),av=e(6557);let au=(0,av.Z)("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);var af=e(8307);let ag=(0,av.Z)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);var ah=e(1137);let ab=(0,av.Z)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var ay=e(9216),aw=e(9635),aj=e(7358),ak=e(2714),az=e(1540),aN=e(3);let aD=(0,av.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var aO=e(8932);function aE(){let[a,i]=(0,n.useState)([]),[e,p]=(0,n.useState)(""),[o,l]=(0,n.useState)("all"),[c,r]=(0,n.useState)("grid"),[s,d]=(0,n.useState)(!0),[m,x]=(0,n.useState)({}),{getRootProps:v,getInputProps:u,isDragActive:f}=aa({onDrop:(0,n.useCallback)(a=>{a.forEach(a=>{let e=Date.now().toString()+Math.random().toString(36).substr(2,9);x(a=>({...a,[e]:0}));let t=setInterval(()=>{x(n=>{let p=n[e]||0;if(p>=100){clearInterval(t);let p={id:e,name:a.name,type:a.type.startsWith("image/")?"image":a.type.includes("document")||a.name.endsWith(".pdf")||a.name.endsWith(".docx")||a.name.endsWith(".xlsx")?"document":"other",size:g(a.size),uploadedAt:new Date().toLocaleString("zh-CN"),uploadedBy:"当前用户",tags:[],version:1,status:"ready"};return i(a=>[p,...a]),{...n,[e]:100}}return{...n,[e]:p+10}})},200)})},[]),accept:{"application/pdf":[".pdf"],"application/msword":[".doc"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"application/vnd.ms-excel":[".xls"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"image/*":[".png",".jpg",".jpeg",".gif"],"application/zip":[".zip"],"text/*":[".txt"]}}),g=a=>{if(0===a)return"0 Bytes";let i=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,i)).toFixed(2))+" "+["Bytes","KB","MB","GB"][i]},h=a=>{switch(a){case"document":return al.Z;case"image":return ac.Z;default:return ar.Z}},b=a=>{let i={processing:{color:"bg-yellow-100 text-yellow-800",label:"处理中"},ready:{color:"bg-green-100 text-green-800",label:"就绪"},error:{color:"bg-red-100 text-red-800",label:"错误"}};return i[a]||i.ready},y=a.filter(a=>{let i=a.name.toLowerCase().includes(e.toLowerCase())||a.tags.some(a=>a.toLowerCase().includes(e.toLowerCase())),t="all"===o||a.type===o;return i&&t});return s?t.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),t.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"加载文件数据中..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[t.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"文件管理"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"管理您的项目文件，支持多种格式上传和版本控制"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx(at.z,{variant:"outline",onClick:()=>r("grid"===c?"list":"grid"),children:"grid"===c?t.jsx(as.Z,{className:"h-4 w-4"}):t.jsx(ad.Z,{className:"h-4 w-4"})}),(0,t.jsxs)(at.z,{children:[t.jsx(am.Z,{className:"mr-2 h-4 w-4"}),"新建文件夹"]})]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)(an.Zb,{className:"mb-8",children:[(0,t.jsxs)(an.Ol,{children:[(0,t.jsxs)(an.ll,{className:"flex items-center",children:[t.jsx(ax.Z,{className:"mr-2 h-5 w-5"}),"文件上传"]}),t.jsx(an.SZ,{children:"支持拖拽上传，或点击选择文件。支持 PDF、Word、Excel、图片等格式"})]}),(0,t.jsxs)(an.aY,{children:[(0,t.jsxs)("div",{...v(),className:`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${f?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 hover:border-gray-400"}`,children:[t.jsx("input",{...u()}),t.jsx(au,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),f?t.jsx("p",{className:"text-blue-600 font-medium",children:"释放文件以开始上传..."}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-2",children:["拖拽文件到此处，或 ",t.jsx("span",{className:"text-blue-600 font-medium",children:"点击选择文件"})]}),t.jsx("p",{className:"text-sm text-gray-500",children:"支持 PDF、Word、Excel、图片、压缩包等格式，单个文件最大 50MB"})]})]}),Object.keys(m).length>0&&t.jsx("div",{className:"mt-4 space-y-2",children:Object.entries(m).map(([a,i])=>t.jsx("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[t.jsx("span",{children:"上传中..."}),(0,t.jsxs)("span",{children:[i,"%"]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${i}%`}})})]})},a))})]})]}),(0,t.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[t.jsx(af.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),t.jsx(ao.I,{placeholder:"搜索文件名或标签...",value:e,onChange:a=>p(a.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:o,onChange:a=>l(a.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600",children:[t.jsx("option",{value:"all",children:"全部类型"}),t.jsx("option",{value:"document",children:"文档"}),t.jsx("option",{value:"image",children:"图片"}),t.jsx("option",{value:"other",children:"其他"})]}),(0,t.jsxs)(at.z,{variant:"outline",children:[t.jsx(ag,{className:"mr-2 h-4 w-4"}),"排序"]}),(0,t.jsxs)(at.z,{variant:"outline",children:[t.jsx(ah.Z,{className:"mr-2 h-4 w-4"}),"筛选"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[t.jsx(an.Zb,{children:t.jsx(an.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:t.jsx(al.Z,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"文档"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.filter(a=>"document"===a.type).length})]})]})})}),t.jsx(an.Zb,{children:t.jsx(an.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:t.jsx(ac.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"图片"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.filter(a=>"image"===a.type).length})]})]})})}),t.jsx(an.Zb,{children:t.jsx(an.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:t.jsx(ar.Z,{className:"h-6 w-6 text-purple-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"其他"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a.filter(a=>"other"===a.type).length})]})]})})}),t.jsx(an.Zb,{children:t.jsx(an.aY,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:t.jsx(ab,{className:"h-6 w-6 text-orange-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[t.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"总大小"}),t.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"24.7 MB"})]})]})})})]}),"grid"===c?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:y.map(a=>{let i=h(a.type),e=b(a.status);return(0,t.jsxs)(an.Zb,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsxs)(an.Ol,{className:"pb-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"p-2 bg-gray-100 rounded-lg",children:t.jsx(i,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[t.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:a.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["v",a.version," • ",a.size]})]})]}),t.jsx(at.z,{variant:"ghost",size:"icon",children:t.jsx(ay.Z,{className:"h-4 w-4"})})]}),t.jsx(ap.C,{className:e.color,children:e.label})]}),t.jsx(an.aY,{className:"pt-0",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center mb-1",children:[t.jsx(aw.Z,{className:"mr-1 h-3 w-3"}),a.uploadedBy]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(aj.Z,{className:"mr-1 h-3 w-3"}),a.uploadedAt]})]}),a.project&&(0,t.jsxs)("div",{className:"text-xs",children:[t.jsx("span",{className:"text-gray-500",children:"项目："}),t.jsx("span",{className:"text-blue-600",children:a.project})]}),a.tags.length>0&&t.jsx("div",{className:"flex flex-wrap gap-1",children:a.tags.map((a,i)=>t.jsx(ap.C,{variant:"secondary",className:"text-xs",children:a},i))}),(0,t.jsxs)("div",{className:"flex space-x-1 pt-2",children:[(0,t.jsxs)(at.z,{variant:"outline",size:"sm",className:"flex-1",children:[t.jsx(ak.Z,{className:"mr-1 h-3 w-3"}),"查看"]}),t.jsx(at.z,{variant:"outline",size:"sm",children:t.jsx(az.Z,{className:"h-3 w-3"})}),t.jsx(at.z,{variant:"outline",size:"sm",children:t.jsx(aN.Z,{className:"h-3 w-3"})})]})]})})]},a.id)})}):t.jsx(an.Zb,{children:t.jsx(an.aY,{className:"p-0",children:t.jsx("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[t.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:(0,t.jsxs)("tr",{children:[t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"文件名"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"大小"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"上传者"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"上传时间"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),t.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:y.map(a=>{let i=h(a.type),e=b(a.status);return(0,t.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-800",children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(i,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["版本 ",a.version]})]})]})}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.size}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.uploadedBy}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.uploadedAt}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.jsx(ap.C,{className:e.color,children:e.label})}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx(at.z,{variant:"ghost",size:"sm",children:t.jsx(ak.Z,{className:"h-4 w-4"})}),t.jsx(at.z,{variant:"ghost",size:"sm",children:t.jsx(az.Z,{className:"h-4 w-4"})}),t.jsx(at.z,{variant:"ghost",size:"sm",children:t.jsx(aN.Z,{className:"h-4 w-4"})}),t.jsx(at.z,{variant:"ghost",size:"sm",children:t.jsx(aD,{className:"h-4 w-4"})})]})})]},a.id)})})]})})})}),0===y.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx(aO.Z,{className:"mx-auto h-12 w-12 text-gray-400"}),t.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"没有找到文件"}),t.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"尝试调整搜索条件或上传新文件"})]})]})]})}},567:(a,i,e)=>{"use strict";e.d(i,{C:()=>l});var t=e(326);e(7577);var n=e(9360),p=e(9310);let o=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:a,variant:i,...e}){return t.jsx("div",{className:(0,p.cn)(o({variant:i}),a),...e})}},2643:(a,i,e)=>{"use strict";e.d(i,{Ol:()=>l,SZ:()=>r,Zb:()=>o,aY:()=>s,ll:()=>c});var t=e(326),n=e(7577),p=e(9310);let o=n.forwardRef(({className:a,...i},e)=>t.jsx("div",{ref:e,className:(0,p.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i}));o.displayName="Card";let l=n.forwardRef(({className:a,...i},e)=>t.jsx("div",{ref:e,className:(0,p.cn)("flex flex-col space-y-1.5 p-6",a),...i}));l.displayName="CardHeader";let c=n.forwardRef(({className:a,...i},e)=>t.jsx("h3",{ref:e,className:(0,p.cn)("text-2xl font-semibold leading-none tracking-tight",a),...i}));c.displayName="CardTitle";let r=n.forwardRef(({className:a,...i},e)=>t.jsx("p",{ref:e,className:(0,p.cn)("text-sm text-muted-foreground",a),...i}));r.displayName="CardDescription";let s=n.forwardRef(({className:a,...i},e)=>t.jsx("div",{ref:e,className:(0,p.cn)("p-6 pt-0",a),...i}));s.displayName="CardContent",n.forwardRef(({className:a,...i},e)=>t.jsx("div",{ref:e,className:(0,p.cn)("flex items-center p-6 pt-0",a),...i})).displayName="CardFooter"},8715:(a,i,e)=>{"use strict";e.d(i,{I:()=>l});var t=e(326),n=e(7577),p=e(1135),o=e(1009);let l=n.forwardRef(({className:a,type:i,...e},n)=>t.jsx("input",{type:i,className:function(...a){return(0,o.m6)((0,p.W)(a))}("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:n,...e}));l.displayName="Input"},4191:(a,i)=>{"use strict";i.__esModule=!0,i.default=function(a,i){if(a&&i){var e=Array.isArray(i)?i:i.split(",");if(0===e.length)return!0;var t=a.name||"",n=(a.type||"").toLowerCase(),p=n.replace(/\/.*$/,"");return e.some(function(a){var i=a.trim().toLowerCase();return"."===i.charAt(0)?t.toLowerCase().endsWith(i):i.endsWith("/*")?p===i.replace(/\/.*$/,""):n===i})}return!0}},7358:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},1540:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2714:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9572:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("File",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}]])},1137:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},924:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},1709:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},9389:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},9216:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},3855:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3685:(a,i,e)=>{"use strict";e.d(i,{Z:()=>t});let t=(0,e(6557).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9899:(a,i,e)=>{"use strict";var t=e(6715);function n(){}function p(){}p.resetWarningCache=n,a.exports=function(){function a(a,i,e,n,p,o){if(o!==t){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function i(){return a}a.isRequired=a;var e={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:i,element:a,elementType:a,instanceOf:i,node:a,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:p,resetWarningCache:n};return e.PropTypes=e,e}},8439:(a,i,e)=>{a.exports=e(9899)()},6715:a=>{"use strict";a.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},6151:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>t});let t=(0,e(8570).createProxy)(String.raw`E:\rjkf\tb-0704-V\app\files\page.tsx#default`)}};var i=require("../../webpack-runtime.js");i.C(a);var e=a=>i(i.s=a),t=i.X(0,[276,555,253],()=>e(4590));module.exports=t})();