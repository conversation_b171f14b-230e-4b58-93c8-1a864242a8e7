import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const search = searchParams.get('search')
    const projectId = searchParams.get('projectId')
    
    // 构建查询条件
    const query: any = {}
    if (type && type !== 'all') {
      query.type = type
    }
    if (projectId) {
      query.projectId = projectId
    }
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ]
    }
    
    // 分页查询
    const skip = (page - 1) * limit
    const files = await db.collection('files')
      .find(query)
      .sort({ uploadedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray()
    
    const total = await db.collection('files').countDocuments(query)
    
    return NextResponse.json({
      success: true,
      data: {
        files,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取文件列表失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const formData = await request.formData()
    
    const file = formData.get('file') as File
    const projectId = formData.get('projectId') as string
    const tags = formData.get('tags') as string
    const description = formData.get('description') as string
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: '没有上传文件' },
        { status: 400 }
      )
    }
    
    // 验证文件类型和大小
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: '文件大小超过50MB限制' },
        { status: 400 }
      )
    }
    
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
      'application/zip'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: '不支持的文件类型' },
        { status: 400 }
      )
    }
    
    // 生成唯一文件名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop()
    const uniqueFileName = `${timestamp}_${randomString}.${fileExtension}`
    
    // 确保上传目录存在
    const uploadDir = join(process.cwd(), 'uploads')
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // 目录已存在，忽略错误
    }
    
    // 保存文件到本地
    const filePath = join(uploadDir, uniqueFileName)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)
    
    // 确定文件类型
    let fileType = 'other'
    if (file.type.startsWith('image/')) {
      fileType = 'image'
    } else if (
      file.type.includes('pdf') ||
      file.type.includes('document') ||
      file.type.includes('word') ||
      file.type.includes('excel') ||
      file.type.includes('sheet')
    ) {
      fileType = 'document'
    }
    
    // 保存文件信息到数据库
    const fileDoc = {
      name: file.name,
      originalName: file.name,
      fileName: uniqueFileName,
      type: fileType,
      mimeType: file.type,
      size: file.size,
      path: `/uploads/${uniqueFileName}`,
      projectId: projectId || null,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      description: description || '',
      version: 1,
      status: 'ready',
      uploadedAt: new Date(),
      uploadedBy: 'current-user', // TODO: 从认证中获取用户信息
      downloads: 0,
      lastAccessed: new Date()
    }
    
    const result = await db.collection('files').insertOne(fileDoc)
    
    // 如果关联了项目，更新项目的文档数量
    if (projectId && ObjectId.isValid(projectId)) {
      await db.collection('projects').updateOne(
        { _id: new ObjectId(projectId) },
        { 
          $inc: { documentCount: 1 },
          $set: { updatedAt: new Date() }
        }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: {
        id: result.insertedId,
        ...fileDoc
      }
    }, { status: 201 })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { success: false, error: '文件上传失败' },
      { status: 500 }
    )
  }
}
