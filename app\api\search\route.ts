import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const { searchParams } = new URL(request.url)
    
    const query = searchParams.get('q')
    const type = searchParams.get('type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    if (!query) {
      return NextResponse.json(
        { error: '搜索关键词不能为空' },
        { status: 400 }
      )
    }

    const searchResults: any[] = []

    // 搜索项目
    if (!type || type === 'all' || type === 'project') {
      const projects = await db.collection('projects')
        .find({
          $or: [
            { name: { $regex: query, $options: 'i' } },
            { description: { $regex: query, $options: 'i' } },
            { tags: { $in: [new RegExp(query, 'i')] } }
          ]
        })
        .limit(limit)
        .toArray()

      projects.forEach(project => {
        searchResults.push({
          id: project._id.toString(),
          title: project.name,
          type: 'project',
          content: project.description,
          excerpt: project.description.substring(0, 200) + '...',
          relevance: calculateRelevance(query, project.name + ' ' + project.description),
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
          createdBy: project.createdBy,
          tags: project.tags || [],
          metadata: {
            status: project.status,
            category: project.category
          }
        })
      })
    }

    // 搜索文档
    if (!type || type === 'all' || type === 'document') {
      const files = await db.collection('files')
        .find({
          $or: [
            { name: { $regex: query, $options: 'i' } },
            { description: { $regex: query, $options: 'i' } },
            { tags: { $in: [new RegExp(query, 'i')] } }
          ]
        })
        .limit(limit)
        .toArray()

      files.forEach(file => {
        searchResults.push({
          id: file._id.toString(),
          title: file.name,
          type: 'document',
          content: file.description || '',
          excerpt: (file.description || '').substring(0, 200) + '...',
          relevance: calculateRelevance(query, file.name + ' ' + (file.description || '')),
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
          createdBy: file.uploadedBy,
          tags: file.tags || [],
          metadata: {
            size: file.size,
            project: file.projectId
          }
        })
      })
    }

    // 搜索模板
    if (!type || type === 'all' || type === 'template') {
      const templates = await db.collection('templates')
        .find({
          $or: [
            { name: { $regex: query, $options: 'i' } },
            { description: { $regex: query, $options: 'i' } },
            { content: { $regex: query, $options: 'i' } },
            { tags: { $in: [new RegExp(query, 'i')] } }
          ]
        })
        .limit(limit)
        .toArray()

      templates.forEach(template => {
        searchResults.push({
          id: template._id.toString(),
          title: template.name,
          type: 'template',
          content: template.content,
          excerpt: template.description.substring(0, 200) + '...',
          relevance: calculateRelevance(query, template.name + ' ' + template.description + ' ' + template.content),
          createdAt: template.createdAt,
          updatedAt: template.updatedAt,
          createdBy: template.createdBy,
          tags: template.tags || [],
          metadata: {
            category: template.category
          }
        })
      })
    }

    // 搜索AI分析结果
    if (!type || type === 'all' || type === 'analysis') {
      const analyses = await db.collection('ai_analyses')
        .find({
          $or: [
            { title: { $regex: query, $options: 'i' } },
            { 'results.summary': { $regex: query, $options: 'i' } },
            { 'results.keyPoints': { $in: [new RegExp(query, 'i')] } }
          ]
        })
        .limit(limit)
        .toArray()

      analyses.forEach(analysis => {
        searchResults.push({
          id: analysis._id.toString(),
          title: analysis.title,
          type: 'analysis',
          content: analysis.results?.summary || '',
          excerpt: (analysis.results?.summary || '').substring(0, 200) + '...',
          relevance: calculateRelevance(query, analysis.title + ' ' + (analysis.results?.summary || '')),
          createdAt: analysis.createdAt,
          updatedAt: analysis.updatedAt,
          createdBy: 'AI分析系统',
          tags: [analysis.type],
          metadata: {
            category: analysis.type
          }
        })
      })
    }

    // 按相关性排序
    searchResults.sort((a, b) => b.relevance - a.relevance)

    // 分页
    const paginatedResults = searchResults.slice(skip, skip + limit)

    return NextResponse.json({
      results: paginatedResults,
      pagination: {
        page,
        limit,
        total: searchResults.length,
        pages: Math.ceil(searchResults.length / limit)
      },
      query,
      type: type || 'all'
    })
  } catch (error) {
    console.error('搜索失败:', error)
    return NextResponse.json(
      { error: '搜索失败' },
      { status: 500 }
    )
  }
}

// 计算相关性得分
function calculateRelevance(query: string, content: string): number {
  const queryLower = query.toLowerCase()
  const contentLower = content.toLowerCase()
  
  let score = 0
  
  // 完全匹配得分最高
  if (contentLower.includes(queryLower)) {
    score += 50
  }
  
  // 单词匹配
  const queryWords = queryLower.split(/\s+/)
  const contentWords = contentLower.split(/\s+/)
  
  queryWords.forEach(queryWord => {
    if (queryWord.length > 1) {
      contentWords.forEach(contentWord => {
        if (contentWord.includes(queryWord)) {
          score += 10
        }
      })
    }
  })
  
  // 标题匹配权重更高
  const titleMatch = content.substring(0, 100).toLowerCase().includes(queryLower)
  if (titleMatch) {
    score += 30
  }
  
  return Math.min(score, 100)
}

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase()
    const body = await request.json()
    
    // 保存搜索历史
    const searchHistory = {
      query: body.query,
      type: body.type || 'all',
      userId: body.userId || 'anonymous',
      timestamp: new Date().toISOString(),
      resultsCount: body.resultsCount || 0
    }
    
    await db.collection('search_history').insertOne(searchHistory)
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('保存搜索历史失败:', error)
    return NextResponse.json(
      { error: '保存搜索历史失败' },
      { status: 500 }
    )
  }
}
