# 文档编辑器导入功能使用说明

## 功能概述

文档编辑器现在支持文件导入功能，可以将外部文件的内容直接导入到编辑器中。

## 支持的文件格式

- **文本文件** (.txt) - 纯文本内容，换行符会转换为HTML换行
- **HTML文件** (.html, .htm) - 直接导入HTML内容
- **Markdown文件** (.md) - 自动转换为HTML格式
- **JSON文件** (.json) - 作为文本内容导入

## 使用方法

### 方法一：点击导入按钮
1. 在编辑器工具栏中点击"导入"按钮
2. 在弹出的文件选择对话框中选择要导入的文件
3. 支持同时选择多个文件
4. 文件内容会自动添加到编辑器中

### 方法二：拖拽导入
1. 直接将文件拖拽到编辑器区域
2. 当拖拽文件到编辑器上方时，会显示蓝色的拖拽提示
3. 释放文件即可完成导入
4. 支持同时拖拽多个文件

## 导入效果

- **纯文本文件**：保持原有格式，换行符转换为HTML换行
- **HTML文件**：直接插入HTML内容，保持原有样式
- **Markdown文件**：自动转换标题、粗体、斜体等格式
- **多个文件**：按选择顺序依次导入，文件间自动添加空行分隔

## 注意事项

1. 导入的内容会添加到编辑器现有内容的末尾
2. 如果编辑器已有内容，新导入的内容前会自动添加两个换行
3. 不支持的文件格式会显示错误提示
4. 文件读取失败时会显示相应的错误信息
5. 导入后记得保存文档以避免内容丢失

## 测试文件

项目根目录下提供了以下测试文件：
- `test-import.txt` - 纯文本测试文件
- `test-import.md` - Markdown格式测试文件  
- `test-import.html` - HTML格式测试文件

您可以使用这些文件来测试导入功能是否正常工作。

## 技术实现

- 使用 `react-dropzone` 库实现拖拽功能
- 使用 `FileReader` API 读取文件内容
- 支持UTF-8编码的文本文件
- 简单的Markdown到HTML转换
- 错误处理和用户反馈机制
