(()=>{var e={};e.id=702,e.ids=[702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},152:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>n}),t(9521),t(6070),t(5866);var a=t(3191),l=t(8716),r=t(7922),d=t.n(r),i=t(5231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let n=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9521)),"E:\\rjkf\\tb-0704-V\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6070)),"E:\\rjkf\\tb-0704-V\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],x=["E:\\rjkf\\tb-0704-V\\app\\dashboard\\page.tsx"],o="/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},6337:(e,s,t)=>{Promise.resolve().then(t.bind(t,5788))},5788:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(326),l=t(7577),r=t(434),d=t(772),i=t(2643),c=t(567),n=t(3855),x=t(8360),o=t(3685),m=t(4659);let h=(0,t(6557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);var u=t(1572),j=t(8378),f=t(3468),p=t(7069),y=t(8998),g=t(6283),N=t(6464),b=t(4230),v=t(7358),w=t(8307);function k(){let[e,s]=(0,l.useState)({totalProjects:0,activeProjects:0,completedProjects:0,totalDocuments:0,aiAnalyses:0,templatesUsed:0}),[t,k]=(0,l.useState)([]),[Z,P]=(0,l.useState)([]),_=e=>({project_created:n.Z,ai_analysis:x.Z,document_uploaded:o.Z,project_completed:m.Z})[e]||h,M=e=>{let s={urgent:{color:"bg-red-100 text-red-800",label:"紧急"},warning:{color:"bg-yellow-100 text-yellow-800",label:"注意"},normal:{color:"bg-green-100 text-green-800",label:"正常"}};return s[e]||s.normal};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[a.jsx("nav",{className:"border-b bg-white dark:bg-gray-800",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[a.jsx("div",{className:"flex items-center",children:(0,a.jsxs)(r.default,{href:"/",className:"flex items-center",children:[a.jsx(u.Z,{className:"h-8 w-8 text-blue-600"}),a.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"投标文件编辑器"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(r.default,{href:"/projects",children:a.jsx(d.z,{variant:"ghost",children:"项目管理"})}),a.jsx(r.default,{href:"/files",children:a.jsx(d.z,{variant:"ghost",children:"文件管理"})}),a.jsx(r.default,{href:"/ai",children:a.jsx(d.z,{variant:"ghost",children:"AI分析"})}),a.jsx(r.default,{href:"/settings",children:a.jsx(d.z,{variant:"ghost",size:"icon",children:a.jsx(j.Z,{className:"h-4 w-4"})})})]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"仪表板"}),a.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"欢迎回来！这里是您的项目概览和最新动态。"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"总项目数"}),a.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.totalProjects}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.jsx(p.Z,{className:"inline h-3 w-3 mr-1"}),"+2 较上月"]})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"进行中"}),a.jsx(y.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.activeProjects}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"+1 较上周"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"已完成"}),a.jsx(m.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.completedProjects}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"+3 较上月"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"文档总数"}),a.jsx(g.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.totalDocuments}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"+12 较上周"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"AI分析"}),a.jsx(x.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.aiAnalyses}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"+8 较上周"})]})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(i.ll,{className:"text-sm font-medium",children:"模板使用"}),a.jsx(N.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"text-2xl font-bold",children:e.templatesUsed}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"+5 较上周"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(h,{className:"mr-2 h-5 w-5"}),"最近活动"]}),a.jsx(i.SZ,{children:"查看您最近的项目活动和操作记录"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"space-y-4",children:t.map(e=>{let s=_(e.type);return(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:a.jsx(s,{className:"h-4 w-4 text-blue-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.title}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.time})]})]},e.id)})}),a.jsx("div",{className:"mt-6",children:a.jsx(r.default,{href:"/activities",children:(0,a.jsxs)(d.z,{variant:"outline",className:"w-full",children:["查看全部活动",a.jsx(b.Z,{className:"ml-2 h-4 w-4"})]})})})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsxs)(i.ll,{className:"flex items-center",children:[a.jsx(v.Z,{className:"mr-2 h-5 w-5"}),"即将到期"]}),a.jsx(i.SZ,{children:"需要关注的项目截止日期"})]}),(0,a.jsxs)(i.aY,{children:[a.jsx("div",{className:"space-y-4",children:Z.map(e=>{let s=M(e.status);return(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:e.project}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.deadline})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.daysLeft,"天"]}),a.jsx(c.C,{className:s.color,children:s.label})]})]},e.id)})}),a.jsx("div",{className:"mt-6",children:a.jsx(r.default,{href:"/projects",children:(0,a.jsxs)(d.z,{variant:"outline",className:"w-full",children:["查看所有项目",a.jsx(b.Z,{className:"ml-2 h-4 w-4"})]})})})]})]}),(0,a.jsxs)(i.Zb,{className:"mt-6",children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"快速操作"}),a.jsx(i.SZ,{children:"常用功能快速入口"})]}),(0,a.jsxs)(i.aY,{className:"space-y-3",children:[a.jsx(r.default,{href:"/projects/new",children:(0,a.jsxs)(d.z,{className:"w-full justify-start",children:[a.jsx(n.Z,{className:"mr-2 h-4 w-4"}),"创建新项目"]})}),a.jsx(r.default,{href:"/files/upload",children:(0,a.jsxs)(d.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"上传文件"]})}),a.jsx(r.default,{href:"/ai/analysis",children:(0,a.jsxs)(d.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"AI分析"]})}),a.jsx(r.default,{href:"/search",children:(0,a.jsxs)(d.z,{variant:"outline",className:"w-full justify-start",children:[a.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"搜索内容"]})})]})]})]})]})]})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>i});var a=t(326);t(7577);var l=t(9360),r=t(9310);let d=(0,l.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...t}){return a.jsx("div",{className:(0,r.cn)(d({variant:s}),e),...t})}},2643:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>i,SZ:()=>n,Zb:()=>d,aY:()=>x,ll:()=>c});var a=t(326),l=t(7577),r=t(9310);let d=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card";let i=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let c=l.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let n=l.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",e),...s}));n.displayName="CardDescription";let x=l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",l.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},4230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6464:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},7358:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},4659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},3855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1572:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3468:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3685:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9521:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`E:\rjkf\tb-0704-V\app\dashboard\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,555,253],()=>t(152));module.exports=a})();