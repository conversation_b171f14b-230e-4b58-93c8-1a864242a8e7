import axios from 'axios'

interface DeepSeekConfig {
  apiKey: string
  baseURL: string
  model: string
}

interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface DeepSeekResponse {
  id: string
  object: string
  created: number
  model: string
  choices: {
    index: number
    message: DeepSeekMessage
    finish_reason: string
  }[]
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

class DeepSeekService {
  private config: DeepSeekConfig

  constructor() {
    this.config = {
      apiKey: process.env.DEEPSEEK_API_KEY || '',
      baseURL: 'https://api.deepseek.com/v1',
      model: 'deepseek-chat'
    }

    if (!this.config.apiKey) {
      throw new Error('DEEPSEEK_API_KEY is required')
    }
  }

  async chat(messages: DeepSeekMessage[], options?: {
    temperature?: number
    max_tokens?: number
    stream?: boolean
  }): Promise<DeepSeekResponse> {
    try {
      const response = await axios.post(
        `${this.config.baseURL}/chat/completions`,
        {
          model: this.config.model,
          messages,
          temperature: options?.temperature || 0.7,
          max_tokens: options?.max_tokens || 4000,
          stream: options?.stream || false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('DeepSeek API error:', error)
      throw new Error('Failed to call DeepSeek API')
    }
  }

  // 分析招标文件
  async analyzeTenderDocument(content: string): Promise<{
    summary: string
    keyPoints: string[]
    requirements: string[]
    suggestions: string[]
  }> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `你是一个专业的招标文件分析专家。请分析提供的招标文件内容，提取关键信息并给出专业建议。

请按照以下格式返回JSON结果：
{
  "summary": "文件概要",
  "keyPoints": ["关键点1", "关键点2", ...],
  "requirements": ["要求1", "要求2", ...],
  "suggestions": ["建议1", "建议2", ...]
}`
      },
      {
        role: 'user',
        content: `请分析以下招标文件内容：\n\n${content}`
      }
    ]

    const response = await this.chat(messages)
    const result = response.choices[0].message.content

    try {
      return JSON.parse(result)
    } catch (error) {
      throw new Error('Failed to parse AI analysis result')
    }
  }

  // 生成投标文件章节
  async generateBidSection(
    sectionType: string,
    requirements: string[],
    companyInfo: any,
    projectInfo: any
  ): Promise<string> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `你是一个专业的投标文件撰写专家。请根据提供的信息生成高质量的投标文件章节内容。

要求：
1. 内容详实，技术论述准确
2. 符合国家相关规范标准要求
3. 结合项目实际特点进行针对性描述
4. 确保内容不少于2000字
5. 包含必要的技术方案和施工组织设计`
      },
      {
        role: 'user',
        content: `请为以下投标项目生成"${sectionType}"章节：

项目信息：
${JSON.stringify(projectInfo, null, 2)}

公司信息：
${JSON.stringify(companyInfo, null, 2)}

具体要求：
${requirements.join('\n')}

请生成详细的章节内容。`
      }
    ]

    const response = await this.chat(messages, { max_tokens: 8000 })
    return response.choices[0].message.content
  }

  // 优化文档内容
  async optimizeContent(content: string, requirements: string[]): Promise<string> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `你是一个专业的文档优化专家。请优化提供的文档内容，使其更加专业、准确、完整。

优化要求：
1. 保持原有结构和主要内容
2. 改进语言表达，使其更加专业
3. 补充必要的技术细节
4. 确保符合招标要求
5. 增强可读性和逻辑性`
      },
      {
        role: 'user',
        content: `请优化以下文档内容：

原始内容：
${content}

优化要求：
${requirements.join('\n')}

请返回优化后的内容。`
      }
    ]

    const response = await this.chat(messages, { max_tokens: 8000 })
    return response.choices[0].message.content
  }
}

export default new DeepSeekService()
