/**
 * @license lucide-react v0.300.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const ShieldX = createLucideIcon("ShieldX", [
  ["path", { d: "M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10", key: "1irkt0" }],
  ["path", { d: "m14.5 9-5 5", key: "1m49dw" }],
  ["path", { d: "m9.5 9 5 5", key: "wyx7zg" }]
]);

export { ShieldX as default };
//# sourceMappingURL=shield-x.js.map
