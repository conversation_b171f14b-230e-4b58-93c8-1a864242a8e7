"use strict";(()=>{var e={};e.id=412,e.ids=[412],e.modules={8013:e=>{e.exports=require("mongodb")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3587:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>j,patchFetch:()=>R,requestAsyncStorage:()=>x,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{DELETE:()=>g,GET:()=>u,POST:()=>l,PUT:()=>d});var o=r(9303),s=r(8716),a=r(670),i=r(7070),p=r(2021),c=r(8013);async function u(e){try{let{db:t}=await (0,p.vO)(),{searchParams:r}=new URL(e.url),n=r.get("category"),o=r.get("type"),s=r.get("search"),a=parseInt(r.get("page")||"1"),c=parseInt(r.get("limit")||"20"),u=(a-1)*c,l={};n&&"all"!==n&&(l.category=n),o&&"all"!==o&&(l.type=o),s&&(l.$or=[{name:{$regex:s,$options:"i"}},{description:{$regex:s,$options:"i"}},{tags:{$in:[RegExp(s,"i")]}}]);let d=await t.collection("templates").find(l).sort({updatedAt:-1}).skip(u).limit(c).toArray(),g=await t.collection("templates").countDocuments(l);return i.NextResponse.json({templates:d.map(e=>({...e,id:e._id.toString(),_id:void 0})),pagination:{page:a,limit:c,total:g,pages:Math.ceil(g/c)}})}catch(e){return console.error("获取模板失败:",e),i.NextResponse.json({error:"获取模板失败"},{status:500})}}async function l(e){try{let{db:t}=await (0,p.vO)(),r=await e.json(),n={name:r.name,description:r.description,category:r.category,type:r.type,content:r.content,tags:r.tags||[],isPublic:r.isPublic||!1,isFavorite:!1,usageCount:0,rating:0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),createdBy:r.createdBy||"系统用户"},o=await t.collection("templates").insertOne(n);return i.NextResponse.json({id:o.insertedId.toString(),...n},{status:201})}catch(e){return console.error("创建模板失败:",e),i.NextResponse.json({error:"创建模板失败"},{status:500})}}async function d(e){try{let{db:t}=await (0,p.vO)(),{id:r,...n}=await e.json();if(!r)return i.NextResponse.json({error:"模板ID不能为空"},{status:400});let o={...n,updatedAt:new Date().toISOString()},s=await t.collection("templates").updateOne({_id:r},{$set:o});if(0===s.matchedCount)return i.NextResponse.json({error:"模板不存在"},{status:404});return i.NextResponse.json({success:!0})}catch(e){return console.error("更新模板失败:",e),i.NextResponse.json({error:"更新模板失败"},{status:500})}}async function g(e){try{let{db:t}=await (0,p.vO)(),{searchParams:r}=new URL(e.url),n=r.get("id");if(!n)return i.NextResponse.json({error:"模板ID不能为空"},{status:400});let o=await t.collection("templates").deleteOne({_id:new c.ObjectId(n)});if(0===o.deletedCount)return i.NextResponse.json({error:"模板不存在"},{status:404});return i.NextResponse.json({success:!0})}catch(e){return console.error("删除模板失败:",e),i.NextResponse.json({error:"删除模板失败"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/templates/route",pathname:"/api/templates",filename:"route",bundlePath:"app/api/templates/route"},resolvedPagePath:"E:\\rjkf\\tb-0704-V\\app\\api\\templates\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:x,staticGenerationAsyncStorage:y,serverHooks:w}=m,j="/api/templates/route";function R(){return(0,a.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:y})}},2021:(e,t,r)=>{let n;async function o(){let e=await n,t=e.db("tender-editor");return{client:e,db:t}}r.d(t,{vO:()=>o}),n=new(r(8013)).MongoClient("mongodb://localhost:27017/tender-editor",{}).connect()}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[276,972],()=>r(3587));module.exports=n})();