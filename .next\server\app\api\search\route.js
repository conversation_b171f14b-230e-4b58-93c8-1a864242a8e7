"use strict";(()=>{var e={};e.id=280,e.ids=[280],e.modules={8013:e=>{e.exports=require("mongodb")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},874:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>g,routeModule:()=>u,serverHooks:()=>y,staticGenerationAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>l});var n=r(9303),s=r(8716),i=r(670),o=r(7070),c=r(2021);async function p(e){try{let{db:t}=await (0,c.vO)(),{searchParams:r}=new URL(e.url),a=r.get("q"),n=r.get("type"),s=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"20"),p=(s-1)*i;if(!a)return o.NextResponse.json({error:"搜索关键词不能为空"},{status:400});let l=[];n&&"all"!==n&&"project"!==n||(await t.collection("projects").find({$or:[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]}).limit(i).toArray()).forEach(e=>{l.push({id:e._id.toString(),title:e.name,type:"project",content:e.description,excerpt:e.description.substring(0,200)+"...",relevance:d(a,e.name+" "+e.description),createdAt:e.createdAt,updatedAt:e.updatedAt,createdBy:e.createdBy,tags:e.tags||[],metadata:{status:e.status,category:e.category}})}),n&&"all"!==n&&"document"!==n||(await t.collection("files").find({$or:[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]}).limit(i).toArray()).forEach(e=>{l.push({id:e._id.toString(),title:e.name,type:"document",content:e.description||"",excerpt:(e.description||"").substring(0,200)+"...",relevance:d(a,e.name+" "+(e.description||"")),createdAt:e.createdAt,updatedAt:e.updatedAt,createdBy:e.uploadedBy,tags:e.tags||[],metadata:{size:e.size,project:e.projectId}})}),n&&"all"!==n&&"template"!==n||(await t.collection("templates").find({$or:[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{content:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]}).limit(i).toArray()).forEach(e=>{l.push({id:e._id.toString(),title:e.name,type:"template",content:e.content,excerpt:e.description.substring(0,200)+"...",relevance:d(a,e.name+" "+e.description+" "+e.content),createdAt:e.createdAt,updatedAt:e.updatedAt,createdBy:e.createdBy,tags:e.tags||[],metadata:{category:e.category}})}),n&&"all"!==n&&"analysis"!==n||(await t.collection("ai_analyses").find({$or:[{title:{$regex:a,$options:"i"}},{"results.summary":{$regex:a,$options:"i"}},{"results.keyPoints":{$in:[RegExp(a,"i")]}}]}).limit(i).toArray()).forEach(e=>{l.push({id:e._id.toString(),title:e.title,type:"analysis",content:e.results?.summary||"",excerpt:(e.results?.summary||"").substring(0,200)+"...",relevance:d(a,e.title+" "+(e.results?.summary||"")),createdAt:e.createdAt,updatedAt:e.updatedAt,createdBy:"AI分析系统",tags:[e.type],metadata:{category:e.type}})}),l.sort((e,t)=>t.relevance-e.relevance);let u=l.slice(p,p+i);return o.NextResponse.json({results:u,pagination:{page:s,limit:i,total:l.length,pages:Math.ceil(l.length/i)},query:a,type:n||"all"})}catch(e){return console.error("搜索失败:",e),o.NextResponse.json({error:"搜索失败"},{status:500})}}function d(e,t){let r=e.toLowerCase(),a=t.toLowerCase(),n=0;a.includes(r)&&(n+=50);let s=r.split(/\s+/),i=a.split(/\s+/);return s.forEach(e=>{e.length>1&&i.forEach(t=>{t.includes(e)&&(n+=10)})}),t.substring(0,100).toLowerCase().includes(r)&&(n+=30),Math.min(n,100)}async function l(e){try{let{db:t}=await (0,c.vO)(),r=await e.json(),a={query:r.query,type:r.type||"all",userId:r.userId||"anonymous",timestamp:new Date().toISOString(),resultsCount:r.resultsCount||0};return await t.collection("search_history").insertOne(a),o.NextResponse.json({success:!0})}catch(e){return console.error("保存搜索历史失败:",e),o.NextResponse.json({error:"保存搜索历史失败"},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},resolvedPagePath:"E:\\rjkf\\tb-0704-V\\app\\api\\search\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:y}=u,h="/api/search/route";function x(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:m})}},2021:(e,t,r)=>{let a;async function n(){let e=await a,t=e.db("tender-editor");return{client:e,db:t}}r.d(t,{vO:()=>n}),a=new(r(8013)).MongoClient("mongodb://localhost:27017/tender-editor",{}).connect()}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[276,972],()=>r(874));module.exports=a})();