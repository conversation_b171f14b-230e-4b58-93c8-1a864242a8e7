'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  Filter, 
  Clock,
  FileText,
  Image,
  File,
  User,
  Calendar,
  Tag,
  Folder,
  Star,
  Eye,
  Download,
  MoreHorizontal,
  SortAsc,
  Grid,
  List,
  Zap,
  Target,
  TrendingUp
} from 'lucide-react'

interface SearchResult {
  id: string
  title: string
  type: 'project' | 'document' | 'template' | 'analysis'
  content: string
  excerpt: string
  relevance: number
  createdAt: string
  updatedAt: string
  createdBy: string
  tags: string[]
  metadata: {
    size?: string
    status?: string
    category?: string
    project?: string
  }
}

export default function SearchPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [filterType, setFilterType] = useState('all')
  const [sortBy, setSortBy] = useState('relevance')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')

  useEffect(() => {
    // 加载搜索历史
    const history = localStorage.getItem('searchHistory')
    if (history) {
      setSearchHistory(JSON.parse(history))
    }
  }, [])

  const performSearch = async () => {
    if (!searchTerm.trim()) return

    setLoading(true)
    
    // 添加到搜索历史
    const newHistory = [searchTerm, ...searchHistory.filter(h => h !== searchTerm)].slice(0, 10)
    setSearchHistory(newHistory)
    localStorage.setItem('searchHistory', JSON.stringify(newHistory))

    // 模拟搜索API调用
    setTimeout(() => {
      const mockResults: SearchResult[] = [
        {
          id: '1',
          title: '某市政道路建设项目技术方案',
          type: 'document',
          content: '本项目为城市主干道建设，全长5.2公里，包含道路工程、排水工程、照明工程等...',
          excerpt: '...道路工程采用沥青混凝土路面，设计时速60km/h，双向六车道...',
          relevance: 95,
          createdAt: '2024-07-01 14:30',
          updatedAt: '2024-07-02 09:15',
          createdBy: '张工程师',
          tags: ['道路建设', '技术方案', '市政工程'],
          metadata: {
            size: '2.5 MB',
            status: '已完成',
            project: '某市政道路建设项目'
          }
        },
        {
          id: '2',
          title: '技术方案标准模板',
          type: 'template',
          content: '适用于建筑工程项目的技术方案编写模板，包含完整的技术要点和实施计划...',
          excerpt: '...项目概述、技术要求、实施方案、质量保证措施、安全管理...',
          relevance: 88,
          createdAt: '2024-06-15 10:30',
          updatedAt: '2024-07-01 14:20',
          createdBy: '系统管理员',
          tags: ['模板', '技术方案', '建筑工程'],
          metadata: {
            size: '15.2 KB',
            category: '技术方案'
          }
        },
        {
          id: '3',
          title: '办公楼装修工程项目',
          type: 'project',
          content: '某企业总部办公楼室内装修改造项目，面积约8000平方米...',
          excerpt: '...包含办公区域、会议室、休息区等功能区域的装修设计和施工...',
          relevance: 82,
          createdAt: '2024-06-20 16:45',
          updatedAt: '2024-07-02 11:30',
          createdBy: '李项目经理',
          tags: ['装修工程', '办公楼', '室内设计'],
          metadata: {
            status: '进行中',
            category: '装修工程'
          }
        },
        {
          id: '4',
          title: '投标文件合规性分析报告',
          type: 'analysis',
          content: 'AI分析结果显示该投标文件整体合规性良好，综合评分85分...',
          excerpt: '...技术方案完整，商务条款规范，但建议补充企业资质证明材料...',
          relevance: 76,
          createdAt: '2024-07-01 16:50',
          updatedAt: '2024-07-01 16:50',
          createdBy: 'AI分析系统',
          tags: ['AI分析', '合规检查', '投标文件'],
          metadata: {
            category: '合规检查'
          }
        }
      ]

      // 根据搜索词过滤结果
      const filtered = mockResults.filter(result => 
        result.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )

      setSearchResults(filtered)
      setLoading(false)
    }, 1000)
  }

  const getTypeInfo = (type: string) => {
    const typeMap = {
      project: { label: '项目', color: 'bg-blue-100 text-blue-800', icon: Folder },
      document: { label: '文档', color: 'bg-green-100 text-green-800', icon: FileText },
      template: { label: '模板', color: 'bg-purple-100 text-purple-800', icon: File },
      analysis: { label: '分析', color: 'bg-orange-100 text-orange-800', icon: TrendingUp }
    }
    return typeMap[type as keyof typeof typeMap] || typeMap.document
  }

  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm) return text
    const regex = new RegExp(`(${searchTerm})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>')
  }

  const filteredResults = searchResults.filter(result => 
    filterType === 'all' || result.type === filterType
  )

  const sortedResults = [...filteredResults].sort((a, b) => {
    switch (sortBy) {
      case 'relevance':
        return b.relevance - a.relevance
      case 'date':
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      case 'title':
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <Search className="mr-3 h-8 w-8 text-blue-600" />
                智能搜索
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                快速搜索项目、文档、模板和分析结果
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索框 */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="搜索项目、文档、模板..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && performSearch()}
                  className="pl-10 text-lg h-12"
                />
              </div>
              <Button onClick={performSearch} disabled={loading} className="h-12 px-8">
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    搜索中...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    搜索
                  </>
                )}
              </Button>
            </div>

            {/* 搜索历史 */}
            {searchHistory.length > 0 && !searchTerm && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  最近搜索
                </h4>
                <div className="flex flex-wrap gap-2">
                  {searchHistory.slice(0, 5).map((term, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSearchTerm(term)
                        performSearch()
                      }}
                      className="text-xs"
                    >
                      <Clock className="mr-1 h-3 w-3" />
                      {term}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* 快速搜索建议 */}
            {!searchTerm && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  热门搜索
                </h4>
                <div className="flex flex-wrap gap-2">
                  {['技术方案', '投标文件', '质量管理', '项目管理', '合规检查'].map((term) => (
                    <Button
                      key={term}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSearchTerm(term)
                        performSearch()
                      }}
                      className="text-xs"
                    >
                      <Star className="mr-1 h-3 w-3" />
                      {term}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 搜索结果 */}
        {searchResults.length > 0 && (
          <>
            {/* 结果统计和筛选 */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="text-sm text-gray-600 dark:text-gray-300">
                找到 <span className="font-medium">{filteredResults.length}</span> 个结果
                {searchTerm && (
                  <>
                    ，关键词：<span className="font-medium">"{searchTerm}"</span>
                  </>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600 text-sm"
                >
                  <option value="all">全部类型</option>
                  <option value="project">项目</option>
                  <option value="document">文档</option>
                  <option value="template">模板</option>
                  <option value="analysis">分析</option>
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600 text-sm"
                >
                  <option value="relevance">相关性</option>
                  <option value="date">更新时间</option>
                  <option value="title">标题</option>
                </select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                >
                  {viewMode === 'list' ? <Grid className="h-4 w-4" /> : <List className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* 搜索结果列表 */}
            <div className="space-y-4">
              {sortedResults.map((result) => {
                const typeInfo = getTypeInfo(result.type)
                const TypeIcon = typeInfo.icon

                return (
                  <Card key={result.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <TypeIcon className="h-5 w-5 text-gray-500" />
                            <h3 
                              className="text-lg font-medium text-gray-900 dark:text-white"
                              dangerouslySetInnerHTML={{ 
                                __html: highlightText(result.title, searchTerm) 
                              }}
                            />
                            <Badge className={typeInfo.color}>
                              {typeInfo.label}
                            </Badge>
                            <div className="flex items-center text-sm text-gray-500">
                              <Target className="mr-1 h-3 w-3" />
                              {result.relevance}% 匹配
                            </div>
                          </div>

                          <p 
                            className="text-gray-600 dark:text-gray-300 mb-3"
                            dangerouslySetInnerHTML={{ 
                              __html: highlightText(result.excerpt, searchTerm) 
                            }}
                          />

                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                            <div className="flex items-center">
                              <User className="mr-1 h-3 w-3" />
                              {result.createdBy}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="mr-1 h-3 w-3" />
                              {result.updatedAt}
                            </div>
                            {result.metadata.size && (
                              <div className="flex items-center">
                                <File className="mr-1 h-3 w-3" />
                                {result.metadata.size}
                              </div>
                            )}
                            {result.metadata.status && (
                              <Badge variant="secondary">
                                {result.metadata.status}
                              </Badge>
                            )}
                          </div>

                          {result.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {result.tags.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  <Tag className="mr-1 h-2 w-2" />
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          <Button variant="outline" size="sm">
                            <Eye className="mr-1 h-3 w-3" />
                            查看
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* 分页 */}
            {sortedResults.length > 10 && (
              <div className="mt-8 flex justify-center">
                <div className="flex space-x-2">
                  <Button variant="outline" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" className="bg-blue-50 text-blue-600">
                    1
                  </Button>
                  <Button variant="outline">
                    2
                  </Button>
                  <Button variant="outline">
                    3
                  </Button>
                  <Button variant="outline">
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}

        {/* 空状态 */}
        {searchTerm && searchResults.length === 0 && !loading && (
          <div className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              没有找到相关结果
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              尝试使用不同的关键词或调整搜索条件
            </p>
            <div className="mt-6">
              <Button variant="outline" onClick={() => setSearchTerm('')}>
                清除搜索
              </Button>
            </div>
          </div>
        )}

        {/* 搜索建议 */}
        {!searchTerm && searchResults.length === 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Zap className="mr-2 h-5 w-5 text-blue-600" />
                  智能搜索
                </CardTitle>
                <CardDescription>
                  使用AI技术提供精准的搜索结果
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• 支持全文搜索</li>
                  <li>• 智能关键词匹配</li>
                  <li>• 相关性排序</li>
                  <li>• 多类型内容搜索</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Target className="mr-2 h-5 w-5 text-green-600" />
                  搜索技巧
                </CardTitle>
                <CardDescription>
                  掌握这些技巧，提高搜索效率
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• 使用具体的关键词</li>
                  <li>• 组合多个相关词汇</li>
                  <li>• 利用标签筛选</li>
                  <li>• 按类型过滤结果</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <TrendingUp className="mr-2 h-5 w-5 text-purple-600" />
                  搜索范围
                </CardTitle>
                <CardDescription>
                  可以搜索的内容类型
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• 项目信息和文档</li>
                  <li>• 投标文件模板</li>
                  <li>• AI分析报告</li>
                  <li>• 历史记录数据</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  )
}
