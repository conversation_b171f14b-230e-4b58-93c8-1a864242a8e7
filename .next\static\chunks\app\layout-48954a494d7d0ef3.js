(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{1546:function(e,t,r){Promise.resolve().then(r.t.bind(r,7960,23)),Promise.resolve().then(r.bind(r,1211)),Promise.resolve().then(r.bind(r,2798)),Promise.resolve().then(r.bind(r,849)),Promise.resolve().then(r.t.bind(r,911,23))},1211:function(e,t,r){"use strict";r.d(t,{default:function(){return T}});var s=r(7437),a=r(2265),i=r(7648),n=r(9376),l=r(2869),d=r(5466),o=r(6475),c=r(8736),x=r(4630),u=r(4972),m=r(8124),f=r(3247),h=r(2369),g=r(8293),v=r(5929),p=r(7226),b=r(4766),y=r(8728),j=r(2489),N=r(7692),w=r(5922);let k=[{name:"仪表板",href:"/dashboard",icon:d.Z},{name:"项目管理",href:"/projects",icon:o.Z},{name:"文件管理",href:"/files",icon:c.Z},{name:"文档编辑",href:"/editor",icon:x.Z},{name:"AI分析",href:"/ai",icon:u.Z},{name:"模板管理",href:"/templates",icon:m.Z},{name:"智能搜索",href:"/search",icon:f.Z}];function T(){let[e,t]=(0,a.useState)(!1),r=(0,n.usePathname)(),{theme:d,setTheme:o}=(0,w.F)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("nav",{className:"hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:border-r lg:border-gray-200 lg:bg-white lg:dark:bg-gray-800 lg:dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto",children:[(0,s.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(c.Z,{className:"w-5 h-5 text-white"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"投标编辑器"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"AI智能投标系统"})]})]})}),(0,s.jsx)("nav",{className:"mt-8 flex-1 px-2 space-y-1",children:k.map(e=>{let t=r===e.href,a=e.icon;return(0,s.jsxs)(i.default,{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ".concat(t?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"),children:[(0,s.jsx)(a,{className:"mr-3 h-5 w-5 flex-shrink-0 ".concat(t?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300")}),e.name]},e.name)})}),(0,s.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,s.jsx)(h.Z,{className:"w-4 h-4 text-gray-600"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:"当前用户"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})})]})}),(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsxs)("div",{className:"flex items-center justify-between bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{type:"button",className:"text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300",onClick:()=>t(!0),children:(0,s.jsx)(g.Z,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"ml-3 flex items-center",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded flex items-center justify-center",children:(0,s.jsx)(c.Z,{className:"w-4 h-4 text-white"})}),(0,s.jsx)("h1",{className:"ml-2 text-lg font-semibold text-gray-900 dark:text-white",children:"投标编辑器"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.z,{variant:"ghost",size:"icon",onClick:()=>o("dark"===d?"light":"dark"),children:"dark"===d?(0,s.jsx)(v.Z,{className:"h-4 w-4"}):(0,s.jsx)(p.Z,{className:"h-4 w-4"})}),(0,s.jsx)(l.z,{variant:"ghost",size:"icon",children:(0,s.jsx)(b.Z,{className:"h-4 w-4"})}),(0,s.jsx)(l.z,{variant:"ghost",size:"icon",children:(0,s.jsx)(y.Z,{className:"h-4 w-4"})})]})]})}),e&&(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsxs)("div",{className:"fixed inset-0 flex z-40",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>t(!1)}),(0,s.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-gray-800",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,s.jsx)("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>t(!1),children:(0,s.jsx)(j.Z,{className:"h-6 w-6 text-white"})})}),(0,s.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(c.Z,{className:"w-5 h-5 text-white"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"投标编辑器"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"AI智能投标系统"})]})]})}),(0,s.jsx)("nav",{className:"mt-8 px-2 space-y-1",children:k.map(e=>{let a=r===e.href,n=e.icon;return(0,s.jsxs)(i.default,{href:e.href,className:"group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors ".concat(a?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"),onClick:()=>t(!1),children:[(0,s.jsx)(n,{className:"mr-4 h-6 w-6 flex-shrink-0 ".concat(a?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300")}),e.name]},e.name)})})]}),(0,s.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,s.jsx)(h.Z,{className:"w-4 h-4 text-gray-600"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:"当前用户"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})})]})]})}),(0,s.jsx)("div",{className:"hidden lg:block lg:pl-64",children:(0,s.jsx)("div",{className:"flex items-center justify-end bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.z,{variant:"ghost",size:"icon",onClick:()=>o("dark"===d?"light":"dark"),children:"dark"===d?(0,s.jsx)(v.Z,{className:"h-4 w-4"}):(0,s.jsx)(p.Z,{className:"h-4 w-4"})}),(0,s.jsx)(l.z,{variant:"ghost",size:"icon",children:(0,s.jsx)(b.Z,{className:"h-4 w-4"})}),(0,s.jsx)(l.z,{variant:"ghost",size:"icon",children:(0,s.jsx)(y.Z,{className:"h-4 w-4"})}),(0,s.jsx)(l.z,{variant:"ghost",size:"icon",children:(0,s.jsx)(N.Z,{className:"h-4 w-4"})})]})})})]})}},2798:function(e,t,r){"use strict";r.d(t,{ThemeProvider:function(){return i}});var s=r(7437);r(2265);var a=r(5922);function i(e){let{children:t,...r}=e;return(0,s.jsx)(a.f,{...r,children:t})}},2869:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var s=r(7437),a=r(2265),i=r(7053),n=r(535),l=r(9554);let d=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:o=!1,...c}=e,x=o?i.g7:"button";return(0,s.jsx)(x,{className:(0,l.cn)(d({variant:a,size:n,className:r})),ref:t,...c})});o.displayName="Button"},849:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return k}});var s=r(7437),a=r(2265);let i=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),x({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function x(e){c=d(c,e),o.forEach(e=>{e(c)})}function u(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>x({type:"DISMISS_TOAST",toastId:r});return x({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>x({type:"UPDATE_TOAST",toast:{...e,id:r}})}}var m=r(6249),f=r(535),h=r(2489),g=r(9554);let v=m.zt,p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(m.l_,{ref:t,className:(0,g.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...a})});p.displayName=m.l_.displayName;let b=(0,f.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=a.forwardRef((e,t)=>{let{className:r,variant:a,...i}=e;return(0,s.jsx)(m.fC,{ref:t,className:(0,g.cn)(b({variant:a}),r),...i})});y.displayName=m.fC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(m.aU,{ref:t,className:(0,g.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...a})}).displayName=m.aU.displayName;let j=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(m.x8,{ref:t,className:(0,g.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...a,children:(0,s.jsx)(h.Z,{className:"h-4 w-4"})})});j.displayName=m.x8.displayName;let N=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(m.Dx,{ref:t,className:(0,g.cn)("text-sm font-semibold",r),...a})});N.displayName=m.Dx.displayName;let w=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(m.dk,{ref:t,className:(0,g.cn)("text-sm opacity-90",r),...a})});function k(){let{toasts:e}=function(){let[e,t]=a.useState(c);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>x({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(v,{children:[e.map(function(e){let{id:t,title:r,description:a,action:i,...n}=e;return(0,s.jsxs)(y,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(N,{children:r}),a&&(0,s.jsx)(w,{children:a})]}),i,(0,s.jsx)(j,{})]},t)}),(0,s.jsx)(p,{})]})}w.displayName=m.dk.displayName},9554:function(e,t,r){"use strict";r.d(t,{cn:function(){return i}});var s=r(1994),a=r(3335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}},7960:function(){}},function(e){e.O(0,[944,635,991,19,971,117,744],function(){return e(e.s=1546)}),_N_E=e.O()}]);