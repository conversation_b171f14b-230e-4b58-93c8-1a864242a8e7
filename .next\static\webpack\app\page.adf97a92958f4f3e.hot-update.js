"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_editor_wysiwyg_editor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/editor/wysiwyg-editor */ \"(app-pages-browser)/./components/editor/wysiwyg-editor.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,Building,CheckCircle,Clock,Database,FileText,FolderOpen,Image,Layers,Plus,Settings,Target,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [currentDocument, setCurrentDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: \"\",\n        title: \"新建投标文件\",\n        content: \"\",\n        wordCount: 0,\n        targetWords: 150000,\n        chapters: []\n    });\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analysisResults, setAnalysisResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activePanel, setActivePanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"files\") // files, analysis, templates, entities\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文档\n        setCurrentDocument((prev)=>({\n                ...prev,\n                id: \"doc_\".concat(Date.now()),\n                content: \"<h1>投标文件</h1><p>请先上传招标文件或从数据库选择文件开始编辑...</p>\"\n            }));\n    }, []);\n    // 文件上传处理\n    const handleFileUpload = async (files)=>{\n        setIsAnalyzing(true);\n        // 模拟AI分析过程\n        setTimeout(()=>{\n            setAnalysisResults({\n                chapters: [\n                    {\n                        id: 1,\n                        title: \"项目概况\",\n                        status: \"analyzed\",\n                        content: \"项目基本信息已解析...\"\n                    },\n                    {\n                        id: 2,\n                        title: \"技术方案\",\n                        status: \"analyzing\",\n                        content: \"正在分析技术要求...\"\n                    },\n                    {\n                        id: 3,\n                        title: \"施工组织设计\",\n                        status: \"pending\",\n                        content: \"等待分析...\"\n                    },\n                    {\n                        id: 4,\n                        title: \"质量保证措施\",\n                        status: \"pending\",\n                        content: \"等待分析...\"\n                    },\n                    {\n                        id: 5,\n                        title: \"安全文明施工\",\n                        status: \"pending\",\n                        content: \"等待分析...\"\n                    },\n                    {\n                        id: 6,\n                        title: \"工期保证措施\",\n                        status: \"pending\",\n                        content: \"等待分析...\"\n                    }\n                ],\n                totalWords: 0,\n                targetWords: 150000\n            });\n            setIsAnalyzing(false);\n        }, 3000);\n    };\n    // 侧边栏功能面板\n    const sidebarPanels = [\n        {\n            id: \"files\",\n            title: \"招标文件\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"上传或选择招标文件\"\n        },\n        {\n            id: \"entities\",\n            title: \"主体管理\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"投标方、供应商、联合体\"\n        },\n        {\n            id: \"analysis\",\n            title: \"AI分析\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"逐章节智能解析\"\n        },\n        {\n            id: \"templates\",\n            title: \"模板库\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"投标文件模板\"\n        },\n        {\n            id: \"diagrams\",\n            title: \"施工图形\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"施工示意图生成\"\n        }\n    ];\n    const quickActions = [\n        {\n            title: \"创建新项目\",\n            description: \"开始一个新的投标项目\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: \"/projects/new\",\n            color: \"bg-blue-600 hover:bg-blue-700\"\n        },\n        {\n            title: \"上传招标文件\",\n            description: \"上传并分析招标文件\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: \"/files/upload\",\n            color: \"bg-green-600 hover:bg-green-700\"\n        },\n        {\n            title: \"浏览模板\",\n            description: \"查看可用的投标模板\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            href: \"/templates\",\n            color: \"bg-purple-600 hover:bg-purple-700\"\n        },\n        {\n            title: \"AI分析\",\n            description: \"使用AI分析现有文档\",\n            icon: _barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/ai/analysis\",\n            color: \"bg-orange-600 hover:bg-orange-700\"\n        }\n    ];\n    const recentProjects = [\n        {\n            id: \"1\",\n            name: \"某市政道路建设项目\",\n            status: \"in_progress\",\n            progress: 75,\n            deadline: \"2024-07-15\",\n            type: \"construction\"\n        },\n        {\n            id: \"2\",\n            name: \"办公楼装修工程\",\n            status: \"review\",\n            progress: 90,\n            deadline: \"2024-07-20\",\n            type: \"construction\"\n        },\n        {\n            id: \"3\",\n            name: \"IT设备采购项目\",\n            status: \"completed\",\n            progress: 100,\n            deadline: \"2024-06-30\",\n            type: \"goods\"\n        }\n    ];\n    const getStatusBadge = (status)=>{\n        const statusMap = {\n            draft: {\n                label: \"草稿\",\n                variant: \"secondary\"\n            },\n            in_progress: {\n                label: \"进行中\",\n                variant: \"default\"\n            },\n            review: {\n                label: \"审核中\",\n                variant: \"outline\"\n            },\n            completed: {\n                label: \"已完成\",\n                variant: \"secondary\"\n            },\n            archived: {\n                label: \"已归档\",\n                variant: \"secondary\"\n            }\n        };\n        return statusMap[status] || statusMap.draft;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white dark:bg-gray-800 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: \"投标文件编辑器\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"当前文档:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: currentDocument.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                currentDocument.wordCount.toLocaleString(),\n                                                \" / \",\n                                                currentDocument.targetWords.toLocaleString(),\n                                                \" 字\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"上传招标文件\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"数据库文件\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"保存文档\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-1 p-2\",\n                                    children: sidebarPanels.map((panel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActivePanel(panel.id),\n                                            className: \"flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors \".concat(activePanel === panel.id ? \"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300\" : \"text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(panel.icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden lg:block\",\n                                                    children: panel.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, panel.id, true, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: [\n                                    activePanel === \"files\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                children: \"招标文件管理\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full justify-start\",\n                                                        onClick: ()=>handleFileUpload([]),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"上传新文件\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"从数据库选择\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                        children: \"最近文件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded border border-gray-200 dark:border-gray-600 text-sm\",\n                                                                children: \"某市政道路建设招标文件.pdf\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded border border-gray-200 dark:border-gray-600 text-sm\",\n                                                                children: \"办公楼装修工程招标文件.docx\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    activePanel === \"entities\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                children: \"主体管理\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border border-gray-200 dark:border-gray-600 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"投标方\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"上传资质文件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border border-gray-200 dark:border-gray-600 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"供应商\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"上传供应商资料\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border border-gray-200 dark:border-gray-600 rounded\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"联合体\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"上传联合体协议\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    activePanel === \"analysis\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                children: \"AI分析进度\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"正在分析招标文件...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this) : analysisResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: analysisResults.chapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 border border-gray-200 dark:border-gray-600 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: chapter.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                chapter.status === \"analyzed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                chapter.status === \"analyzing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 62\n                                                                }, this),\n                                                                chapter.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 60\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, chapter.id, false, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"请先上传招标文件进行分析\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    activePanel === \"templates\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                children: \"投标模板库\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: \"建筑工程投标模板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                children: \"适用于建筑施工项目\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: \"市政工程投标模板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                children: \"适用于市政基础设施\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: \"装修工程投标模板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                children: \"适用于装修装饰项目\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    activePanel === \"diagrams\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                children: \"施工图形生成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"施工示意图\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"工程节点大样图\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"现场总平面布置图\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"w-full justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"主要设备布置图\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_editor_wysiwyg_editor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            initialContent: currentDocument.content,\n                            onContentChange: (content)=>{\n                                const wordCount = content.replace(/<[^>]*>/g, \"\").length;\n                                setCurrentDocument((prev)=>({\n                                        ...prev,\n                                        content,\n                                        wordCount\n                                    }));\n                            },\n                            onSave: (content)=>{\n                                console.log(\"保存文档:\", content);\n                            },\n                            placeholder: \"开始编写您的投标文件...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-64 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                            children: \"文档状态\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"当前字数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: currentDocument.wordCount.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"目标字数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: currentDocument.targetWords.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-600 h-2 rounded-full transition-all\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(currentDocument.wordCount / currentDocument.targetWords * 100, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"完成度: \",\n                                                        Math.round(currentDocument.wordCount / currentDocument.targetWords * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                analysisResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                            children: \"章节进度\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: analysisResults.chapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: chapter.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        chapter.status === \"analyzed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 57\n                                                        }, this),\n                                                        chapter.status === \"analyzing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 58\n                                                        }, this),\n                                                        chapter.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_Building_CheckCircle_Clock_Database_FileText_FolderOpen_Image_Layers_Plus_Settings_Target_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 56\n                                                        }, this)\n                                                    ]\n                                                }, chapter.id, true, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                            children: \"AI建议\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-2 border-blue-500\",\n                                                    children: \"建议增加施工组织设计的详细描述\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border-l-2 border-yellow-500\",\n                                                    children: \"质量保证措施需要更多技术细节\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-green-50 dark:bg-green-900/20 rounded border-l-2 border-green-500\",\n                                                    children: \"安全文明施工方案符合规范要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\rjkf\\\\tb-0704-V\\\\app\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"eZWD7No4G57mFBSXIFkdj1PdNM4=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});