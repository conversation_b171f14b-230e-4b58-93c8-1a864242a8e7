'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import WysiwygEditor from '@/components/editor/wysiwyg-editor'
import { 
  FileText, 
  Save, 
  Share, 
  Download, 
  Upload,
  Eye,
  Edit,
  Settings,
  Clock,
  User,
  Tag,
  Folder,
  Star,
  MoreHorizontal,
  ArrowLeft,
  Zap,
  Brain
} from 'lucide-react'

interface Document {
  id: string
  title: string
  content: string
  projectId?: string
  projectName?: string
  tags: string[]
  status: 'draft' | 'review' | 'approved' | 'published'
  createdAt: string
  updatedAt: string
  createdBy: string
  version: number
  wordCount: number
}

export default function EditorPage() {
  const [document, setDocument] = useState<Document>({
    id: '1',
    title: '新建投标文档',
    content: '',
    tags: ['投标文件', '技术方案'],
    status: 'draft',
    createdAt: '2024-07-04 10:30',
    updatedAt: '2024-07-04 10:30',
    createdBy: '当前用户',
    version: 1,
    wordCount: 0
  })
  
  const [isEditing, setIsEditing] = useState(true)
  const [saving, setSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date>(new Date())

  const handleContentChange = (content: string) => {
    setDocument(prev => ({
      ...prev,
      content,
      wordCount: content.replace(/<[^>]*>/g, '').length,
      updatedAt: new Date().toISOString()
    }))
  }

  const handleSave = async (content: string) => {
    setSaving(true)
    try {
      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setDocument(prev => ({
        ...prev,
        content,
        updatedAt: new Date().toISOString()
      }))
      
      setLastSaved(new Date())
      setIsEditing(false)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleTitleChange = (title: string) => {
    setDocument(prev => ({ ...prev, title }))
  }

  const getStatusInfo = (status: string) => {
    const statusMap = {
      draft: { label: '草稿', color: 'bg-gray-100 text-gray-800' },
      review: { label: '审核中', color: 'bg-yellow-100 text-yellow-800' },
      approved: { label: '已批准', color: 'bg-green-100 text-green-800' },
      published: { label: '已发布', color: 'bg-blue-100 text-blue-800' }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.draft
  }

  const statusInfo = getStatusInfo(document.status)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部工具栏 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回
              </Button>
              
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6 text-blue-600" />
                <div>
                  <Input
                    value={document.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    className="text-lg font-medium border-none p-0 h-auto bg-transparent focus:ring-0"
                    placeholder="文档标题"
                  />
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span>版本 {document.version}</span>
                    <span>•</span>
                    <span>{document.wordCount} 字</span>
                    <span>•</span>
                    <Badge className={statusInfo.color}>
                      {statusInfo.label}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Brain className="mr-2 h-4 w-4" />
                AI助手
              </Button>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                预览
              </Button>
              <Button variant="outline" size="sm">
                <Share className="mr-2 h-4 w-4" />
                分享
              </Button>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
              <Button 
                onClick={() => handleSave(document.content)} 
                disabled={saving}
                size="sm"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    保存
                  </>
                )}
              </Button>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 编辑器主区域 */}
          <div className="lg:col-span-3">
            <WysiwygEditor
              initialContent={document.content}
              onSave={handleSave}
              onContentChange={handleContentChange}
              placeholder="开始编写您的投标文档内容..."
            />
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 文档信息 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">文档信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">创建时间</span>
                  <span>{document.createdAt}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">更新时间</span>
                  <span>{document.updatedAt}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">创建者</span>
                  <span>{document.createdBy}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">字数统计</span>
                  <span>{document.wordCount}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">最后保存</span>
                  <span>{lastSaved.toLocaleTimeString()}</span>
                </div>
              </CardContent>
            </Card>

            {/* 标签管理 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Tag className="mr-2 h-4 w-4" />
                  标签
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-3">
                  {document.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  添加标签
                </Button>
              </CardContent>
            </Card>

            {/* 项目关联 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Folder className="mr-2 h-4 w-4" />
                  项目关联
                </CardTitle>
              </CardHeader>
              <CardContent>
                {document.projectName ? (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{document.projectName}</span>
                    <Button variant="ghost" size="sm">
                      更改
                    </Button>
                  </div>
                ) : (
                  <Button variant="outline" size="sm" className="w-full">
                    关联项目
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* AI助手 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Zap className="mr-2 h-4 w-4" />
                  AI助手
                </CardTitle>
                <CardDescription>
                  使用AI功能提升文档质量
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Brain className="mr-2 h-4 w-4" />
                  内容优化
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  语法检查
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Star className="mr-2 h-4 w-4" />
                  质量评估
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Settings className="mr-2 h-4 w-4" />
                  合规检查
                </Button>
              </CardContent>
            </Card>

            {/* 版本历史 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  版本历史
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <div>
                      <div className="font-medium">版本 1.0</div>
                      <div className="text-gray-500">2024-07-04 10:30</div>
                    </div>
                    <Button variant="ghost" size="sm">
                      查看
                    </Button>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-3">
                  查看所有版本
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
