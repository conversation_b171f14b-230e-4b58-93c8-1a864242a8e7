(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},657:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(908),t(6070),t(5866);var r=t(3191),a=t(8716),l=t(7922),i=t.n(l),d=t(5231),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,908)),"E:\\rjkf\\tb-0704-V\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,6070)),"E:\\rjkf\\tb-0704-V\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],o=["E:\\rjkf\\tb-0704-V\\app\\page.tsx"],x="/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9316:(e,s,t)=>{Promise.resolve().then(t.bind(t,8743))},8743:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(326),a=t(7577),l=t(434),i=t(772),d=t(2643),n=t(567),c=t(6283),o=t(8360),x=t(8307),m=t(3685),h=t(3855),p=t(1572),u=t(8378),j=t(4230),f=t(3468),g=t(8998),b=t(6464);function v(){let[e,s]=(0,a.useState)({totalProjects:0,activeProjects:0,completedProjects:0,totalDocuments:0}),t=[{icon:c.Z,title:"智能文档生成",description:"基于AI技术自动生成高质量投标文件，支持多种模板和自定义配置",color:"bg-blue-500"},{icon:o.Z,title:"AI分析引擎",description:"深度分析招标文件，提取关键信息，生成针对性的投标策略",color:"bg-purple-500"},{icon:x.Z,title:"智能搜索",description:"全文搜索和语义搜索，快速找到相关文档和模板内容",color:"bg-green-500"},{icon:m.Z,title:"文件管理",description:"支持多种格式文件上传，版本控制，协作编辑功能",color:"bg-orange-500"}],v=[{title:"创建新项目",description:"开始一个新的投标项目",icon:h.Z,href:"/projects/new",color:"bg-blue-600 hover:bg-blue-700"},{title:"上传招标文件",description:"上传并分析招标文件",icon:m.Z,href:"/files/upload",color:"bg-green-600 hover:bg-green-700"},{title:"浏览模板",description:"查看可用的投标模板",icon:c.Z,href:"/templates",color:"bg-purple-600 hover:bg-purple-700"},{title:"AI分析",description:"使用AI分析现有文档",icon:o.Z,href:"/ai/analysis",color:"bg-orange-600 hover:bg-orange-700"}],y=e=>{let s={draft:{label:"草稿",variant:"secondary"},in_progress:{label:"进行中",variant:"default"},review:{label:"审核中",variant:"outline"},completed:{label:"已完成",variant:"secondary"},archived:{label:"已归档",variant:"secondary"}};return s[e]||s.draft};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[r.jsx("nav",{className:"border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[r.jsx("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"flex-shrink-0 flex items-center",children:[r.jsx(p.Z,{className:"h-8 w-8 text-blue-600"}),r.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"投标文件编辑器"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(l.default,{href:"/dashboard",children:r.jsx(i.z,{variant:"ghost",children:"仪表板"})}),r.jsx(l.default,{href:"/projects",children:r.jsx(i.z,{variant:"ghost",children:"项目管理"})}),r.jsx(l.default,{href:"/settings",children:r.jsx(i.z,{variant:"ghost",size:"icon",children:r.jsx(u.Z,{className:"h-4 w-4"})})})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"欢迎使用投标文件编辑器"}),r.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8",children:"所见即所得的智能投标文件生成系统，让投标更简单、更高效"}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4",children:[r.jsx(l.default,{href:"/projects/new",children:(0,r.jsxs)(i.z,{size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:[r.jsx(h.Z,{className:"mr-2 h-5 w-5"}),"创建新项目"]})}),r.jsx(l.default,{href:"/dashboard",children:(0,r.jsxs)(i.z,{size:"lg",variant:"outline",children:["进入仪表板",r.jsx(j.Z,{className:"ml-2 h-5 w-5"})]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"总项目数"}),r.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.totalProjects}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+2 较上月"})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"进行中项目"}),r.jsx(g.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.activeProjects}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+1 较上周"})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"已完成项目"}),r.jsx(b.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.completedProjects}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+3 较上月"})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(d.ll,{className:"text-sm font-medium",children:"文档总数"}),r.jsx(c.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.totalDocuments}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+12 较上周"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"核心功能"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:t.map((e,s)=>(0,r.jsxs)(d.Zb,{className:"hover:shadow-lg transition-shadow",children:[r.jsx(d.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`p-2 rounded-lg ${e.color}`,children:r.jsx(e.icon,{className:"h-6 w-6 text-white"})}),r.jsx(d.ll,{className:"text-lg",children:e.title})]})}),r.jsx(d.aY,{children:r.jsx(d.SZ,{className:"text-sm",children:e.description})})]},s))})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"快速操作"}),r.jsx("div",{className:"space-y-4",children:v.map((e,s)=>r.jsx(l.default,{href:e.href,children:r.jsx(d.Zb,{className:"hover:shadow-md transition-shadow cursor-pointer",children:r.jsx(d.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:`p-2 rounded-lg ${e.color}`,children:r.jsx(e.icon,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium",children:e.title}),r.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})]})]})})})},s))})]})]}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"最近项目"}),r.jsx(l.default,{href:"/projects",children:(0,r.jsxs)(i.z,{variant:"outline",children:["查看全部",r.jsx(j.Z,{className:"ml-2 h-4 w-4"})]})})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{id:"1",name:"某市政道路建设项目",status:"in_progress",progress:75,deadline:"2024-07-15",type:"construction"},{id:"2",name:"办公楼装修工程",status:"review",progress:90,deadline:"2024-07-20",type:"construction"},{id:"3",name:"IT设备采购项目",status:"completed",progress:100,deadline:"2024-06-30",type:"goods"}].map(e=>(0,r.jsxs)(d.Zb,{className:"hover:shadow-lg transition-shadow",children:[(0,r.jsxs)(d.Ol,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[r.jsx(d.ll,{className:"text-lg",children:e.name}),r.jsx(n.C,{...y(e.status),children:y(e.status).label})]}),(0,r.jsxs)(d.SZ,{children:["截止日期: ",e.deadline]})]}),(0,r.jsxs)(d.aY,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"进度"}),(0,r.jsxs)("span",{children:[e.progress,"%"]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.progress}%`}})})]}),r.jsx("div",{className:"mt-4",children:r.jsx(l.default,{href:`/projects/${e.id}`,children:r.jsx(i.z,{variant:"outline",size:"sm",className:"w-full",children:"查看详情"})})})]})]},e.id))})]})]})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>d});var r=t(326);t(7577);var a=t(9360),l=t(9310);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...t}){return r.jsx("div",{className:(0,l.cn)(i({variant:s}),e),...t})}},2643:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>d,SZ:()=>c,Zb:()=>i,aY:()=>o,ll:()=>n});var r=t(326),a=t(7577),l=t(9310);let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let n=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},4230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6464:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},8998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},3855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1572:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3468:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},3685:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(6557).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},908:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(8570).createProxy)(String.raw`E:\rjkf\tb-0704-V\app\page.tsx#default`)}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,555,253],()=>t(657));module.exports=r})();